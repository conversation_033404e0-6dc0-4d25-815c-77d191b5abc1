{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Export My Data</h1>
            <a href="{{ url_for('dashboard.settings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Settings
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download me-2"></i>Data Export Request
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Your Right to Data Portability</h6>
                    <p class="mb-0">
                        Under GDPR Article 20, you have the right to receive your personal data in a structured, 
                        commonly used format. This export includes all data associated with your account.
                    </p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold">What's Included in Your Export:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Account information</li>
                            <li><i class="fas fa-check text-success me-2"></i>Job applications history</li>
                            <li><i class="fas fa-check text-success me-2"></i>Automation sessions</li>
                            <li><i class="fas fa-check text-success me-2"></i>User preferences and settings</li>
                            <li><i class="fas fa-check text-success me-2"></i>Recent activity logs</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">Export Statistics:</h6>
                        <div class="bg-light rounded p-3">
                            <div class="row g-3 text-center">
                                <div class="col-6">
                                    <div class="fw-bold">{{ current_user.job_applications.count() }}</div>
                                    <small class="text-muted">Applications</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold">{{ current_user.automation_sessions.count() }}</div>
                                    <small class="text-muted">Sessions</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form method="POST" id="exportForm">
                    <div class="mb-4">
                        <label class="form-label fw-bold">Export Format</label>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="format" id="formatJson" value="json" checked>
                                    <label class="form-check-label" for="formatJson">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-code text-primary me-2"></i>
                                            <div>
                                                <div class="fw-bold">JSON Format</div>
                                                <small class="text-muted">Machine-readable, structured data</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="format" id="formatCsv" value="csv">
                                    <label class="form-check-label" for="formatCsv">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-table text-success me-2"></i>
                                            <div>
                                                <div class="fw-bold">CSV Format</div>
                                                <small class="text-muted">Spreadsheet-compatible tables</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Information</h6>
                        <ul class="mb-0">
                            <li>The export file will contain all your personal data</li>
                            <li>Keep the file secure and delete it when no longer needed</li>
                            <li>The download link will expire after 24 hours</li>
                            <li>You can request a new export at any time</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('security.privacy_policy') }}" class="btn btn-outline-info">
                            <i class="fas fa-shield-alt me-2"></i>Privacy Policy
                        </a>
                        
                        <button type="submit" class="btn btn-primary" id="exportBtn">
                            <i class="fas fa-download me-2"></i>Generate Export
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Recent Exports -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Export Requests
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-3">
                    <i class="fas fa-inbox text-muted fa-2x mb-3"></i>
                    <p class="text-muted">No recent export requests</p>
                    <small class="text-muted">Your export history will appear here</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.getElementById('exportForm').addEventListener('submit', function(e) {
    const exportBtn = document.getElementById('exportBtn');
    
    // Show loading state
    exportBtn.disabled = true;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Export...';
    
    // Note: The form will submit normally, but we show loading state
    // The server will return the file download
});

// Reset button state if user navigates back
window.addEventListener('pageshow', function(event) {
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.disabled = false;
        exportBtn.innerHTML = '<i class="fas fa-download me-2"></i>Generate Export';
    }
});
</script>
{% endblock %}
