{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Delete My Account</h1>
            <a href="{{ url_for('dashboard.settings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Settings
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm border-danger">
            <div class="card-header bg-danger bg-opacity-10 border-0">
                <h5 class="card-title mb-0 text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Permanent Account Deletion
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-skull-crossbones me-2"></i>Warning: This Action Cannot Be Undone</h6>
                    <p class="mb-0">
                        Deleting your account will permanently remove all your data from our systems. 
                        This action is irreversible and complies with GDPR Article 17 (Right to Erasure).
                    </p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold text-danger">What Will Be Deleted:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-times text-danger me-2"></i>Your account and profile</li>
                            <li><i class="fas fa-times text-danger me-2"></i>All job applications ({{ current_user.job_applications.count() }})</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Automation sessions ({{ current_user.automation_sessions.count() }})</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Uploaded resume and documents</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Preferences and settings</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Activity logs and history</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold text-info">Before You Delete:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-download text-info me-2"></i>
                                <a href="{{ url_for('security.data_export') }}">Export your data</a> for backup
                            </li>
                            <li><i class="fas fa-credit-card text-info me-2"></i>Cancel any active subscriptions</li>
                            <li><i class="fas fa-envelope text-info me-2"></i>Save important job application details</li>
                            <li><i class="fas fa-file-alt text-info me-2"></i>Download your resume if needed</li>
                        </ul>
                    </div>
                </div>
                
                {% if current_user.has_active_subscription() %}
                <div class="alert alert-warning">
                    <h6><i class="fas fa-credit-card me-2"></i>Active Subscription Detected</h6>
                    <p class="mb-2">You have an active subscription that will be cancelled upon account deletion.</p>
                    <a href="{{ url_for('dashboard.billing') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-cog me-2"></i>Manage Subscription
                    </a>
                </div>
                {% endif %}
                
                <div class="bg-light rounded p-4 mb-4">
                    <h6 class="fw-bold">Account Summary</h6>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="fw-bold">{{ current_user.job_applications.count() }}</div>
                                <small class="text-muted">Applications</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="fw-bold">{{ current_user.automation_sessions.count() }}</div>
                                <small class="text-muted">Sessions</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="fw-bold">{{ (current_user.created_at | daysago) }}</div>
                                <small class="text-muted">Days Active</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="fw-bold">
                                    {% if current_user.has_active_subscription() %}
                                        Premium
                                    {% elif current_user.is_trial_active() %}
                                        Trial
                                    {% else %}
                                        Free
                                    {% endif %}
                                </div>
                                <small class="text-muted">Plan</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form method="POST" id="deletionForm">
                    <div class="mb-4">
                        <label class="form-label fw-bold">Confirmation Required</label>
                        <p class="text-muted">To confirm deletion, please type <strong>"DELETE MY ACCOUNT"</strong> in the box below:</p>
                        <input type="text" class="form-control" name="confirmation" id="confirmationInput" 
                               placeholder="Type: DELETE MY ACCOUNT" required>
                        <div class="form-text">This confirmation is case-sensitive</div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="understandCheckbox" required>
                            <label class="form-check-label" for="understandCheckbox">
                                I understand that this action is permanent and cannot be undone
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportCheckbox" required>
                            <label class="form-check-label" for="exportCheckbox">
                                I have exported my data or do not need a backup
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="subscriptionCheckbox" required>
                            <label class="form-check-label" for="subscriptionCheckbox">
                                I understand my subscription will be cancelled
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="{{ url_for('security.data_export') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-download me-2"></i>Export Data First
                            </a>
                            <a href="{{ url_for('security.privacy_policy') }}" class="btn btn-outline-info">
                                <i class="fas fa-shield-alt me-2"></i>Privacy Policy
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                            <i class="fas fa-trash me-2"></i>Delete My Account Permanently
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Alternative Options -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Alternative Options
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">Before deleting your account, consider these alternatives:</p>
                
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-pause text-warning fa-2x mb-2"></i>
                            <h6>Deactivate Account</h6>
                            <p class="small text-muted">Temporarily disable your account without losing data</p>
                            <button class="btn btn-outline-warning btn-sm" onclick="deactivateAccount()">
                                Deactivate
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-user-slash text-info fa-2x mb-2"></i>
                            <h6>Clear Personal Data</h6>
                            <p class="small text-muted">Remove personal information but keep statistics</p>
                            <button class="btn btn-outline-info btn-sm" onclick="anonymizeData()">
                                Anonymize
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <i class="fas fa-headset text-success fa-2x mb-2"></i>
                            <h6>Contact Support</h6>
                            <p class="small text-muted">Discuss your concerns with our team</p>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-success btn-sm">
                                Get Help
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
const confirmationInput = document.getElementById('confirmationInput');
const checkboxes = document.querySelectorAll('input[type="checkbox"]');
const deleteBtn = document.getElementById('deleteBtn');

function validateForm() {
    const confirmationValid = confirmationInput.value === 'DELETE MY ACCOUNT';
    const allCheckboxesChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    deleteBtn.disabled = !(confirmationValid && allCheckboxesChecked);
    
    if (confirmationValid && allCheckboxesChecked) {
        deleteBtn.classList.remove('btn-danger');
        deleteBtn.classList.add('btn-outline-danger');
    } else {
        deleteBtn.classList.remove('btn-outline-danger');
        deleteBtn.classList.add('btn-danger');
    }
}

confirmationInput.addEventListener('input', validateForm);
checkboxes.forEach(cb => cb.addEventListener('change', validateForm));

document.getElementById('deletionForm').addEventListener('submit', function(e) {
    if (!confirm('Are you absolutely sure you want to delete your account? This action cannot be undone.')) {
        e.preventDefault();
        return;
    }
    
    deleteBtn.disabled = true;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting Account...';
});

function deactivateAccount() {
    alert('Account deactivation feature coming soon! Contact support for assistance.');
}

function anonymizeData() {
    alert('Data anonymization feature coming soon! Contact support for assistance.');
}
</script>
{% endblock %}
