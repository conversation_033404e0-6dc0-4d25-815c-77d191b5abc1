"""
Celery tasks for AutoApply.co.nz automation
Background job processing for job applications
"""

from datetime import datetime, timedelta
from celery import current_task
from flask import current_app
from app import db, celery
from app.models import User, AutomationSession, JobApplication
from app.automation.selenium_driver import SeekWebDriver
from app.automation.job_processor import JobProcessor
from app.automation.cover_letter_generator import CoverL<PERSON>rGenerator

@celery.task(bind=True)
def run_job_automation(self, user_id, job_preferences=None):
    """
    Main task to run job application automation for a user
    
    Args:
        user_id: ID of the user to run automation for
        job_preferences: Optional job preferences override
    
    Returns:
        dict: Results of the automation session
    """
    session = None
    driver_wrapper = None
    
    try:
        # Update task state
        self.update_state(state='PROGRESS', meta={'status': 'Starting automation session'})
        
        # Get user and validate
        user = User.query.get(user_id)
        if not user:
            raise Exception(f"User {user_id} not found")
        
        if not user.can_apply_to_jobs():
            raise Exception("User does not have active subscription or trial")
        
        # Check daily application limit
        applications_today = user.get_applications_today()
        max_daily = current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50)
        
        if applications_today >= max_daily:
            raise Exception(f"Daily application limit of {max_daily} reached")
        
        # Create automation session
        session = AutomationSession(user_id=user_id, status='running')
        db.session.add(session)
        db.session.commit()
        
        current_app.logger.info(f"Starting automation session {session.id} for user {user_id}")
        
        # Update task state
        self.update_state(
            state='PROGRESS', 
            meta={
                'status': 'Setting up browser',
                'session_id': session.id
            }
        )
        
        # Setup WebDriver
        driver_wrapper = SeekWebDriver(user_id=user_id, headless=True)
        if not driver_wrapper.start_driver():
            raise Exception("Failed to start WebDriver")
        
        # Navigate to Seek
        if not driver_wrapper.navigate_to_seek():
            raise Exception("Failed to navigate to Seek.co.nz")
        
        # Update task state
        self.update_state(
            state='PROGRESS', 
            meta={
                'status': 'Processing job applications',
                'session_id': session.id
            }
        )
        
        # Initialize job processor
        job_processor = JobProcessor(driver_wrapper, user_id, session.id)
        
        # Process jobs
        results = job_processor.process_jobs(job_preferences)
        
        # Update session with results
        session.jobs_found = results.get('jobs_found', 0)
        session.applications_submitted = results.get('applications_submitted', 0)
        session.applications_failed = results.get('applications_failed', 0)
        session.rate_limited = results.get('rate_limited', False)
        
        if results.get('rate_limited'):
            session.status = 'stopped'
            session.error_message = 'Rate limited by Seek.co.nz'
        else:
            session.mark_completed()
        
        db.session.commit()
        
        current_app.logger.info(f"Automation session {session.id} completed: {results}")
        
        # Update task state
        self.update_state(
            state='SUCCESS', 
            meta={
                'status': 'Completed',
                'session_id': session.id,
                'results': results
            }
        )
        
        return results
        
    except Exception as e:
        error_msg = str(e)
        current_app.logger.error(f"Automation task failed for user {user_id}: {error_msg}")
        
        # Update session if it exists
        if session:
            session.mark_failed(error_msg)
            db.session.commit()
        
        # Update task state
        self.update_state(
            state='FAILURE',
            meta={
                'status': 'Failed',
                'error': error_msg,
                'session_id': session.id if session else None
            }
        )
        
        raise
        
    finally:
        # Cleanup WebDriver
        if driver_wrapper:
            driver_wrapper.cleanup()

@celery.task
def cleanup_old_sessions():
    """Clean up old automation sessions and data"""
    try:
        # Delete sessions older than 30 days
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        old_sessions = AutomationSession.query.filter(
            AutomationSession.started_at < cutoff_date
        ).all()
        
        for session in old_sessions:
            db.session.delete(session)
        
        db.session.commit()
        
        current_app.logger.info(f"Cleaned up {len(old_sessions)} old automation sessions")
        
        return {'cleaned_sessions': len(old_sessions)}
        
    except Exception as e:
        current_app.logger.error(f"Failed to cleanup old sessions: {e}")
        raise

@celery.task
def send_daily_summary_emails():
    """Send daily summary emails to users"""
    try:
        # Get users who want daily summaries
        users = User.query.join(UserSettings).filter(
            UserSettings.daily_summary_email == True,
            User.is_active == True,
            User.is_verified == True
        ).all()
        
        summary_count = 0
        
        for user in users:
            try:
                # Get yesterday's applications
                yesterday = datetime.utcnow().date() - timedelta(days=1)
                applications = user.job_applications.filter(
                    db.func.date(JobApplication.applied_at) == yesterday
                ).all()
                
                if applications:
                    # Send summary email (implement email sending)
                    send_user_daily_summary(user, applications)
                    summary_count += 1
                    
            except Exception as e:
                current_app.logger.error(f"Failed to send summary to user {user.id}: {e}")
                continue
        
        current_app.logger.info(f"Sent {summary_count} daily summary emails")
        
        return {'summaries_sent': summary_count}
        
    except Exception as e:
        current_app.logger.error(f"Failed to send daily summaries: {e}")
        raise

@celery.task
def process_subscription_renewals():
    """Process subscription renewals and handle failed payments"""
    try:
        from app.models import Subscription
        
        # Get subscriptions that need renewal processing
        subscriptions = Subscription.query.filter(
            Subscription.status.in_(['active', 'past_due']),
            Subscription.current_period_end <= datetime.utcnow()
        ).all()
        
        processed_count = 0
        
        for subscription in subscriptions:
            try:
                # Process renewal with Stripe (implement Stripe integration)
                process_stripe_renewal(subscription)
                processed_count += 1
                
            except Exception as e:
                current_app.logger.error(f"Failed to process renewal for subscription {subscription.id}: {e}")
                continue
        
        current_app.logger.info(f"Processed {processed_count} subscription renewals")
        
        return {'renewals_processed': processed_count}
        
    except Exception as e:
        current_app.logger.error(f"Failed to process subscription renewals: {e}")
        raise

def send_user_daily_summary(user, applications):
    """Send daily summary email to user"""
    # Implement email sending logic
    pass

def process_stripe_renewal(subscription):
    """Process Stripe subscription renewal"""
    # Implement Stripe renewal logic
    pass

@celery.task
def test_automation_health():
    """Health check task for automation system"""
    try:
        # Test WebDriver setup
        driver_wrapper = SeekWebDriver(user_id=999999, headless=True)
        
        if driver_wrapper.start_driver():
            driver_wrapper.cleanup()
            health_status = 'healthy'
        else:
            health_status = 'unhealthy'
        
        current_app.logger.info(f"Automation health check: {health_status}")
        
        return {'status': health_status, 'timestamp': datetime.utcnow().isoformat()}
        
    except Exception as e:
        current_app.logger.error(f"Automation health check failed: {e}")
        return {'status': 'unhealthy', 'error': str(e), 'timestamp': datetime.utcnow().isoformat()}

# Periodic task configuration
from celery.schedules import crontab

celery.conf.beat_schedule = {
    'cleanup-old-sessions': {
        'task': 'app.automation.tasks.cleanup_old_sessions',
        'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM
    },
    'send-daily-summaries': {
        'task': 'app.automation.tasks.send_daily_summary_emails',
        'schedule': crontab(hour=8, minute=0),  # Daily at 8 AM
    },
    'process-renewals': {
        'task': 'app.automation.tasks.process_subscription_renewals',
        'schedule': crontab(hour=1, minute=0),  # Daily at 1 AM
    },
    'health-check': {
        'task': 'app.automation.tasks.test_automation_health',
        'schedule': crontab(minute='*/30'),  # Every 30 minutes
    },
}

celery.conf.timezone = 'Pacific/Auckland'
