"""
Security middleware for AutoApply.co.nz
Implements security headers, CSRF protection, and request validation
"""

from flask import request, g, current_app, abort, jsonify
from functools import wraps
import time
import hashlib
import hmac
from app.security.rate_limiter import rate_limiter, get_client_ip, log_security_event

class SecurityMiddleware:
    """Security middleware for request processing"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security middleware with Flask app"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        
        # Security configuration
        app.config.setdefault('SECURITY_HEADERS', True)
        app.config.setdefault('RATE_LIMITING', True)
        app.config.setdefault('IP_BLOCKING', True)
        app.config.setdefault('REQUEST_VALIDATION', True)
    
    def before_request(self):
        """Process request before handling"""
        try:
            # Get client information
            g.client_ip = get_client_ip()
            g.user_agent = request.headers.get('User-Agent', '')
            g.request_start_time = time.time()
            
            # Check if IP is blocked
            if current_app.config.get('IP_BLOCKING', True):
                is_blocked, block_info = rate_limiter.is_ip_blocked(g.client_ip)
                if is_blocked:
                    log_security_event(
                        'blocked_ip_access_attempt',
                        f"Blocked IP {g.client_ip} attempted access",
                        severity='WARNING',
                        additional_data=block_info
                    )
                    return jsonify({
                        'error': 'Access denied',
                        'reason': 'IP address blocked',
                        'expires_in': block_info.get('expires_in', 0)
                    }), 429
            
            # Validate request
            if current_app.config.get('REQUEST_VALIDATION', True):
                validation_result = self._validate_request()
                if validation_result:
                    return validation_result
            
            # Check for suspicious patterns
            if self._detect_suspicious_activity():
                log_security_event(
                    'suspicious_activity_detected',
                    f"Suspicious activity from {g.client_ip}",
                    severity='WARNING',
                    additional_data={
                        'user_agent': g.user_agent,
                        'path': request.path,
                        'method': request.method
                    }
                )
                
                # Rate limit suspicious requests more aggressively
                is_limited, retry_after = rate_limiter.is_rate_limited(
                    g.client_ip, 'suspicious', None
                )
                if is_limited:
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'retry_after': retry_after
                    }), 429
            
        except Exception as e:
            current_app.logger.error(f"Security middleware error: {e}")
            # Don't block requests on middleware errors
            pass
    
    def after_request(self, response):
        """Process response after handling"""
        try:
            # Add security headers
            if current_app.config.get('SECURITY_HEADERS', True):
                response = self._add_security_headers(response)
            
            # Log request completion
            if hasattr(g, 'request_start_time'):
                duration = time.time() - g.request_start_time
                
                # Log slow requests
                if duration > 5.0:  # 5 seconds
                    log_security_event(
                        'slow_request',
                        f"Slow request: {request.path} took {duration:.2f}s",
                        severity='WARNING',
                        additional_data={
                            'duration': duration,
                            'path': request.path,
                            'method': request.method,
                            'status_code': response.status_code
                        }
                    )
            
            # Log failed authentication attempts
            if response.status_code == 401:
                rate_limiter.record_failed_attempt(
                    g.client_ip, 
                    'authentication',
                    getattr(g, 'current_user', {}).get('id')
                )
            
        except Exception as e:
            current_app.logger.error(f"Security middleware after_request error: {e}")
        
        return response
    
    def _validate_request(self):
        """Validate incoming request for security issues"""
        try:
            # Check request size
            max_content_length = current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)  # 16MB
            if request.content_length and request.content_length > max_content_length:
                log_security_event(
                    'oversized_request',
                    f"Oversized request from {g.client_ip}: {request.content_length} bytes",
                    severity='WARNING'
                )
                return jsonify({'error': 'Request too large'}), 413
            
            # Check for malicious patterns in URL
            malicious_patterns = [
                '../', '..\\', '<script', 'javascript:', 'vbscript:',
                'onload=', 'onerror=', 'eval(', 'exec(', 'system(',
                'union select', 'drop table', 'delete from'
            ]
            
            url_lower = request.url.lower()
            for pattern in malicious_patterns:
                if pattern in url_lower:
                    log_security_event(
                        'malicious_pattern_detected',
                        f"Malicious pattern '{pattern}' in URL from {g.client_ip}",
                        severity='WARNING',
                        additional_data={'url': request.url, 'pattern': pattern}
                    )
                    
                    # Block IP after multiple malicious requests
                    rate_limiter.record_failed_attempt(g.client_ip, 'malicious_request')
                    return jsonify({'error': 'Invalid request'}), 400
            
            # Validate JSON requests
            if request.is_json and request.content_length > 0:
                try:
                    # Attempt to parse JSON to validate format
                    request.get_json()
                except Exception:
                    log_security_event(
                        'invalid_json_request',
                        f"Invalid JSON from {g.client_ip}",
                        severity='INFO'
                    )
                    return jsonify({'error': 'Invalid JSON format'}), 400
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"Request validation error: {e}")
            return None
    
    def _detect_suspicious_activity(self):
        """Detect suspicious activity patterns"""
        try:
            # Check for bot-like user agents
            bot_patterns = [
                'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
                'python-requests', 'go-http-client', 'java/', 'php/'
            ]
            
            user_agent_lower = g.user_agent.lower()
            for pattern in bot_patterns:
                if pattern in user_agent_lower:
                    # Allow legitimate bots but log them
                    if any(legit in user_agent_lower for legit in ['googlebot', 'bingbot', 'facebookexternalhit']):
                        return False
                    return True
            
            # Check for rapid requests (more than 10 per minute)
            if rate_limiter.redis:
                rapid_key = f"rapid_requests:{g.client_ip}"
                count = rate_limiter.redis.incr(rapid_key)
                if count == 1:
                    rate_limiter.redis.expire(rapid_key, 60)  # 1 minute window
                
                if count > 10:
                    return True
            
            # Check for unusual request patterns
            if request.method in ['PUT', 'DELETE', 'PATCH'] and not request.path.startswith('/api/'):
                return True
            
            return False
            
        except Exception as e:
            current_app.logger.error(f"Suspicious activity detection error: {e}")
            return False
    
    def _add_security_headers(self, response):
        """Add security headers to response"""
        try:
            # Content Security Policy
            csp_policy = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
                "https://cdn.jsdelivr.net https://js.stripe.com https://checkout.stripe.com; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
                "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; "
                "img-src 'self' data: https:; "
                "connect-src 'self' https://api.stripe.com; "
                "frame-src https://checkout.stripe.com https://js.stripe.com; "
                "object-src 'none'; "
                "base-uri 'self';"
            )
            response.headers['Content-Security-Policy'] = csp_policy
            
            # Other security headers
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
            
            # HSTS (only for HTTPS)
            if request.is_secure:
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            # Remove server information
            response.headers.pop('Server', None)
            
            return response
            
        except Exception as e:
            current_app.logger.error(f"Security headers error: {e}")
            return response

def require_api_key(f):
    """Decorator to require API key for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            log_security_event(
                'missing_api_key',
                f"API request without key from {get_client_ip()}",
                severity='WARNING'
            )
            return jsonify({'error': 'API key required'}), 401
        
        # Validate API key (implement your validation logic)
        if not _validate_api_key(api_key):
            log_security_event(
                'invalid_api_key',
                f"Invalid API key from {get_client_ip()}",
                severity='WARNING',
                additional_data={'api_key_prefix': api_key[:8] + '...' if len(api_key) > 8 else api_key}
            )
            return jsonify({'error': 'Invalid API key'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function

def _validate_api_key(api_key):
    """Validate API key (implement your validation logic)"""
    # For now, return True (implement proper API key validation)
    return True

def validate_csrf_token():
    """Validate CSRF token for state-changing requests"""
    if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
        token = request.headers.get('X-CSRF-Token') or request.form.get('csrf_token')
        
        if not token:
            log_security_event(
                'missing_csrf_token',
                f"Request without CSRF token from {get_client_ip()}",
                severity='WARNING'
            )
            return False
        
        # Validate CSRF token (implement your validation logic)
        return _validate_csrf_token(token)
    
    return True

def _validate_csrf_token(token):
    """Validate CSRF token (implement your validation logic)"""
    # For now, return True (implement proper CSRF validation)
    return True

def sanitize_input(data):
    """Sanitize user input to prevent XSS and injection attacks"""
    if isinstance(data, str):
        # Basic HTML escaping
        data = data.replace('<', '&lt;').replace('>', '&gt;')
        data = data.replace('"', '&quot;').replace("'", '&#x27;')
        data = data.replace('&', '&amp;')
        
        # Remove potentially dangerous patterns
        dangerous_patterns = [
            'javascript:', 'vbscript:', 'onload=', 'onerror=',
            'eval(', 'exec(', 'system(', 'import('
        ]
        
        data_lower = data.lower()
        for pattern in dangerous_patterns:
            if pattern in data_lower:
                data = data.replace(pattern, '')
        
        return data.strip()
    
    elif isinstance(data, dict):
        return {key: sanitize_input(value) for key, value in data.items()}
    
    elif isinstance(data, list):
        return [sanitize_input(item) for item in data]
    
    return data
