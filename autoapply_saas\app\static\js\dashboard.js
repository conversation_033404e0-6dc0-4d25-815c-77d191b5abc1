/**
 * Dashboard JavaScript for AutoApply.co.nz
 * Handles automation controls, real-time updates, and user interactions
 */

class DashboardManager {
    constructor() {
        this.currentTaskId = null;
        this.statusCheckInterval = null;
        this.statsUpdateInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.startStatsUpdates();
        this.checkRunningAutomation();
    }

    bindEvents() {
        // Start automation button
        const startBtn = document.getElementById('startAutomationBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.showAutomationModal());
        }

        // Confirm start automation
        const confirmBtn = document.getElementById('confirmStartAutomation');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.startAutomation());
        }

        // Stop automation button
        const stopBtn = document.getElementById('stopAutomationBtn');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopAutomation());
        }

        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Auto-hide sidebar on mobile when clicking outside
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.dashboard-sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 991.98 && 
                sidebar && sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) && 
                !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });
    }

    showAutomationModal() {
        const modal = new bootstrap.Modal(document.getElementById('automationModal'));
        modal.show();
    }

    async startAutomation() {
        const form = document.getElementById('automationForm');
        const formData = new FormData(form);
        
        const preferences = {
            keywords: formData.get('keywords') || '',
            location: formData.get('location') || '',
            generate_cover_letters: formData.get('generate_cover_letters') === 'on'
        };

        try {
            this.setButtonLoading('confirmStartAutomation', true);
            
            const response = await fetch('/dashboard/start-automation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({ preferences })
            });

            const data = await response.json();

            if (data.success) {
                this.currentTaskId = data.task_id;
                this.hideAutomationModal();
                this.showToast('Automation started successfully!', 'success');
                this.startStatusChecking();
                this.updateAutomationStatus('starting');
            } else {
                this.showToast(data.error || 'Failed to start automation', 'error');
            }
        } catch (error) {
            console.error('Error starting automation:', error);
            this.showToast('Network error. Please try again.', 'error');
        } finally {
            this.setButtonLoading('confirmStartAutomation', false);
        }
    }

    async stopAutomation() {
        if (!confirm('Are you sure you want to stop the automation?')) {
            return;
        }

        try {
            // Implementation would depend on your backend API
            this.showToast('Automation stopped', 'info');
            this.stopStatusChecking();
            this.updateAutomationStatus('stopped');
        } catch (error) {
            console.error('Error stopping automation:', error);
            this.showToast('Failed to stop automation', 'error');
        }
    }

    async checkAutomationStatus() {
        if (!this.currentTaskId) return;

        try {
            const response = await fetch(`/dashboard/automation-status/${this.currentTaskId}`);
            const data = await response.json();

            if (data.state === 'SUCCESS' || data.state === 'FAILURE') {
                this.stopStatusChecking();
                
                if (data.state === 'SUCCESS') {
                    this.showToast('Automation completed successfully!', 'success');
                    this.updateAutomationStatus('completed', data.results);
                } else {
                    this.showToast('Automation failed: ' + (data.error || 'Unknown error'), 'error');
                    this.updateAutomationStatus('failed');
                }
                
                this.currentTaskId = null;
                this.refreshStats();
            } else if (data.state === 'PROGRESS') {
                this.updateAutomationStatus('running', data);
            }
        } catch (error) {
            console.error('Error checking automation status:', error);
        }
    }

    startStatusChecking() {
        this.stopStatusChecking(); // Clear any existing interval
        this.statusCheckInterval = setInterval(() => {
            this.checkAutomationStatus();
        }, 3000); // Check every 3 seconds
    }

    stopStatusChecking() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    updateAutomationStatus(status, data = null) {
        const statusContainer = document.getElementById('automationStatus');
        if (!statusContainer) return;

        let html = '';

        switch (status) {
            case 'starting':
                html = `
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>
                                <div class="fw-bold">Starting Automation</div>
                                <small class="text-muted">Initializing browser and searching for jobs...</small>
                            </div>
                        </div>
                        <button id="stopAutomationBtn" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-stop me-2"></i>Stop
                        </button>
                    </div>
                `;
                break;

            case 'running':
                const statusText = data?.status || 'Processing jobs...';
                html = `
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div>
                                <div class="fw-bold">Automation Running</div>
                                <small class="text-muted">${statusText}</small>
                            </div>
                        </div>
                        <button id="stopAutomationBtn" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-stop me-2"></i>Stop
                        </button>
                    </div>
                    <div class="mt-3">
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 100%"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted">${statusText}</small>
                            <small class="text-muted">Running...</small>
                        </div>
                    </div>
                `;
                break;

            case 'completed':
                const results = data || {};
                html = `
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle text-success fa-2x mb-3"></i>
                        <div class="fw-bold text-success">Automation Completed</div>
                        <small class="text-muted">
                            ${results.applications_submitted || 0} applications submitted successfully
                        </small>
                    </div>
                `;
                break;

            case 'failed':
                html = `
                    <div class="text-center py-3">
                        <i class="fas fa-times-circle text-danger fa-2x mb-3"></i>
                        <div class="fw-bold text-danger">Automation Failed</div>
                        <small class="text-muted">Please try again or contact support</small>
                    </div>
                `;
                break;

            default:
                html = `
                    <div class="text-center py-3">
                        <i class="fas fa-pause-circle text-muted fa-2x mb-3"></i>
                        <div class="fw-bold text-muted">No Active Automation</div>
                        <small class="text-muted">Click "Start Automation" to begin applying to jobs</small>
                    </div>
                `;
        }

        statusContainer.innerHTML = html;

        // Re-bind stop button event
        const stopBtn = document.getElementById('stopAutomationBtn');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopAutomation());
        }
    }

    async refreshStats() {
        try {
            const response = await fetch('/api/dashboard/statistics');
            const stats = await response.json();

            // Update stat cards
            this.updateElement('applicationsToday', stats.applications_today || 0);
            this.updateElement('applicationsThisMonth', stats.applications_this_month || 0);
            this.updateElement('successRate', (stats.success_rate || 0) + '%');
            this.updateElement('avgApplications', stats.avg_applications_per_session || 0);

        } catch (error) {
            console.error('Error refreshing stats:', error);
        }
    }

    startStatsUpdates() {
        // Update stats every 30 seconds
        this.statsUpdateInterval = setInterval(() => {
            this.refreshStats();
        }, 30000);
    }

    checkRunningAutomation() {
        // Check if there's already a running automation session
        const runningStatus = document.querySelector('#automationStatus .spinner-border');
        if (runningStatus) {
            // There's already a running session, start checking its status
            this.startStatusChecking();
        }
    }

    hideAutomationModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('automationModal'));
        if (modal) {
            modal.hide();
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.dashboard-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    }

    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.dataset.originalText = originalText;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting...';
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || button.innerHTML;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('automationToast');
        const toastBody = toast.querySelector('.toast-body');
        
        // Set toast content and style
        toastBody.textContent = message;
        
        // Remove existing type classes
        toast.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info');
        
        // Add appropriate class based on type
        switch (type) {
            case 'success':
                toast.classList.add('bg-success', 'text-white');
                break;
            case 'error':
                toast.classList.add('bg-danger', 'text-white');
                break;
            case 'warning':
                toast.classList.add('bg-warning');
                break;
            default:
                toast.classList.add('bg-info', 'text-white');
        }

        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DashboardManager();
});

// Handle window resize for responsive sidebar
window.addEventListener('resize', () => {
    const sidebar = document.querySelector('.dashboard-sidebar');
    if (window.innerWidth > 991.98 && sidebar) {
        sidebar.classList.remove('show');
    }
});
