{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">System Logs</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>Clear Filters
                </button>
                <button class="btn btn-outline-primary" onclick="exportLogs()">
                    <i class="fas fa-download me-2"></i>Export Logs
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Log Level</label>
                        <select class="form-select" name="level">
                            <option value="">All Levels</option>
                            <option value="INFO" {{ 'selected' if level == 'INFO' else '' }}>INFO</option>
                            <option value="WARNING" {{ 'selected' if level == 'WARNING' else '' }}>WARNING</option>
                            <option value="ERROR" {{ 'selected' if level == 'ERROR' else '' }}>ERROR</option>
                            <option value="CRITICAL" {{ 'selected' if level == 'CRITICAL' else '' }}>CRITICAL</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date</label>
                        <input type="date" class="form-control" name="date" value="{{ date_filter }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Search Message</label>
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search in log messages..." value="{{ search }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Log Level Summary -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-info-circle text-info fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ logs.items | selectattr('level', 'equalto', 'INFO') | list | length }}</h4>
                <p class="text-muted mb-0">Info Messages</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle text-warning fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ logs.items | selectattr('level', 'equalto', 'WARNING') | list | length }}</h4>
                <p class="text-muted mb-0">Warnings</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-times-circle text-danger fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ logs.items | selectattr('level', 'equalto', 'ERROR') | list | length }}</h4>
                <p class="text-muted mb-0">Errors</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-skull text-dark fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ logs.items | selectattr('level', 'equalto', 'CRITICAL') | list | length }}</h4>
                <p class="text-muted mb-0">Critical</p>
            </div>
        </div>
    </div>
</div>

<!-- Logs Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>System Logs ({{ logs.total }} total)
                </h5>
            </div>
            <div class="card-body">
                {% if logs.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Level</th>
                                    <th>Message</th>
                                    <th>Source</th>
                                    <th>User</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs.items %}
                                <tr class="log-row" data-level="{{ log.level.lower() }}">
                                    <td>
                                        <div class="fw-bold">{{ log.created_at.strftime('%H:%M:%S') }}</div>
                                        <small class="text-muted">{{ log.created_at.strftime('%b %d, %Y') }}</small>
                                    </td>
                                    <td>
                                        {% if log.level == 'INFO' %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-info-circle me-1"></i>{{ log.level }}
                                            </span>
                                        {% elif log.level == 'WARNING' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>{{ log.level }}
                                            </span>
                                        {% elif log.level == 'ERROR' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times-circle me-1"></i>{{ log.level }}
                                            </span>
                                        {% elif log.level == 'CRITICAL' %}
                                            <span class="badge bg-dark">
                                                <i class="fas fa-skull me-1"></i>{{ log.level }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ log.level }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="log-message" style="max-width: 400px;">
                                            <div class="text-truncate" title="{{ log.message }}">
                                                {{ log.message }}
                                            </div>
                                            {% if log.extra_data %}
                                                <small class="text-muted">
                                                    <i class="fas fa-paperclip me-1"></i>Additional data available
                                                </small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if log.source %}
                                            <span class="badge bg-light text-dark">{{ log.source }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.user %}
                                            <div class="d-flex align-items-center">
                                                <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 24px; height: 24px; font-size: 0.7rem;">
                                                    {{ log.user.first_name[0].upper() }}
                                                </div>
                                                <div>
                                                    <div class="small fw-bold">{{ log.user.first_name }} {{ log.user.last_name }}</div>
                                                    <small class="text-muted">{{ log.user.email }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="viewLogDetails({{ log.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if logs.pages > 1 %}
                    <nav aria-label="Logs pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if logs.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.logs', page=logs.prev_num, level=level, date=date_filter, search=search) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in logs.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != logs.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('admin.logs', page=page_num, level=level, date=date_filter, search=search) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if logs.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.logs', page=logs.next_num, level=level, date=date_filter, search=search) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt text-muted fa-3x mb-4"></i>
                        <h4 class="text-muted">No Logs Found</h4>
                        <p class="text-muted mb-4">No logs match your current filters.</p>
                        <a href="{{ url_for('admin.logs') }}" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>Clear Filters
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>Log Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="logDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function clearFilters() {
    window.location.href = '{{ url_for("admin.logs") }}';
}

function exportLogs() {
    // Implementation for log export
    showAdminToast('Log export feature coming soon!', 'info');
}

async function viewLogDetails(logId) {
    try {
        // For now, show a placeholder modal
        // In production, this would fetch detailed log information
        const modalContent = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">Log Information</h6>
                    <p><strong>ID:</strong> ${logId}</p>
                    <p><strong>Level:</strong> <span class="badge bg-info">INFO</span></p>
                    <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Source:</strong> automation</p>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold">Context</h6>
                    <p><strong>Session ID:</strong> abc123</p>
                    <p><strong>IP Address:</strong> ***********</p>
                    <p><strong>User Agent:</strong> Mozilla/5.0...</p>
                </div>
            </div>
            <hr>
            <div>
                <h6 class="fw-bold">Message</h6>
                <div class="bg-light rounded p-3">
                    <code>Sample log message for demonstration purposes.</code>
                </div>
            </div>
            <div class="mt-3">
                <h6 class="fw-bold">Additional Data</h6>
                <div class="bg-light rounded p-3">
                    <pre><code>{
  "user_id": 123,
  "action": "start_automation",
  "preferences": {
    "keywords": "software developer",
    "location": "Auckland"
  }
}</code></pre>
                </div>
            </div>
        `;
        
        document.getElementById('logDetailsContent').innerHTML = modalContent;
        new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
        
    } catch (error) {
        console.error('Error loading log details:', error);
        showAdminToast('Failed to load log details', 'error');
    }
}

// Auto-refresh logs every 30 seconds for real-time monitoring
setInterval(() => {
    // Only refresh if we're on the first page and no filters are applied
    const urlParams = new URLSearchParams(window.location.search);
    if (!urlParams.has('page') && !urlParams.has('level') && !urlParams.has('search') && !urlParams.has('date')) {
        window.location.reload();
    }
}, 30000);
</script>

<style>
.log-row[data-level="error"] {
    background-color: rgba(220, 53, 69, 0.05);
}

.log-row[data-level="warning"] {
    background-color: rgba(255, 193, 7, 0.05);
}

.log-row[data-level="critical"] {
    background-color: rgba(108, 117, 125, 0.1);
}

.log-message {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.avatar {
    font-size: 0.7rem;
    font-weight: 600;
}
</style>
{% endblock %}
