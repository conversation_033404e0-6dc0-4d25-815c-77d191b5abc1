"""
Database models for AutoApply.co.nz SaaS application
"""

from datetime import datetime, timed<PERSON>ta
from flask import current_app
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import json
from app import db, login_manager

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

class User(UserMixin, db.Model):
    """User model for authentication and profile management"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    
    # Account status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.<PERSON>, default=False, nullable=False)
    email_verified_at = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = db.Column(db.DateTime)
    
    # Trial and subscription info
    trial_started_at = db.Column(db.DateTime)
    trial_applications_used = db.Column(db.Integer, default=0)
    
    # Relationships
    subscription = db.relationship('Subscription', backref='user', uselist=False, cascade='all, delete-orphan')
    job_applications = db.relationship('JobApplication', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    user_settings = db.relationship('UserSettings', backref='user', uselist=False, cascade='all, delete-orphan')
    automation_sessions = db.relationship('AutomationSession', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def generate_verification_token(self, expires_in=3600):
        """Generate email verification token"""
        return jwt.encode(
            {'verify_email': self.id, 'exp': datetime.utcnow() + timedelta(seconds=expires_in)},
            current_app.config['SECRET_KEY'],
            algorithm='HS256'
        )
    
    def verify_email_token(self, token):
        """Verify email verification token"""
        try:
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            if data.get('verify_email') != self.id:
                return False
        except:
            return False
        self.is_verified = True
        self.email_verified_at = datetime.utcnow()
        return True
    
    def start_trial(self):
        """Start free trial period"""
        if not self.trial_started_at:
            self.trial_started_at = datetime.utcnow()
            self.trial_applications_used = 0
    
    def is_trial_active(self):
        """Check if trial is still active"""
        if not self.trial_started_at:
            return False
        trial_end = self.trial_started_at + timedelta(days=current_app.config['FREE_TRIAL_DAYS'])
        return datetime.utcnow() < trial_end and self.trial_applications_used < current_app.config['FREE_TRIAL_APPLICATIONS']
    
    def has_active_subscription(self):
        """Check if user has active subscription"""
        return self.subscription and self.subscription.is_active()
    
    def can_apply_to_jobs(self):
        """Check if user can apply to jobs (trial or subscription)"""
        return self.is_trial_active() or self.has_active_subscription()
    
    def get_applications_today(self):
        """Get number of applications submitted today"""
        today = datetime.utcnow().date()
        return self.job_applications.filter(
            db.func.date(JobApplication.applied_at) == today
        ).count()
    
    def __repr__(self):
        return f'<User {self.email}>'

class Subscription(db.Model):
    """Subscription model for billing management"""
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Stripe integration
    stripe_customer_id = db.Column(db.String(100), unique=True)
    stripe_subscription_id = db.Column(db.String(100), unique=True)
    stripe_price_id = db.Column(db.String(100))
    
    # Subscription details
    status = db.Column(db.String(20), nullable=False)  # active, canceled, past_due, etc.
    current_period_start = db.Column(db.DateTime)
    current_period_end = db.Column(db.DateTime)
    cancel_at_period_end = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    canceled_at = db.Column(db.DateTime)
    
    def is_active(self):
        """Check if subscription is currently active"""
        return self.status == 'active' and (
            not self.current_period_end or 
            datetime.utcnow() < self.current_period_end
        )
    
    def days_remaining(self):
        """Get days remaining in current period"""
        if not self.current_period_end:
            return 0
        delta = self.current_period_end - datetime.utcnow()
        return max(0, delta.days)
    
    def __repr__(self):
        return f'<Subscription {self.user.email} - {self.status}>'

class JobApplication(db.Model):
    """Job application tracking model"""
    __tablename__ = 'job_applications'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    automation_session_id = db.Column(db.Integer, db.ForeignKey('automation_sessions.id'))
    
    # Job details
    job_title = db.Column(db.String(200), nullable=False)
    company_name = db.Column(db.String(200), nullable=False)
    job_url = db.Column(db.Text, nullable=False)
    job_description = db.Column(db.Text)
    
    # Application details
    application_status = db.Column(db.String(50), default='submitted')  # submitted, failed, rate_limited
    cover_letter_generated = db.Column(db.Boolean, default=False)
    cover_letter_content = db.Column(db.Text)
    
    # Timestamps
    applied_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Metadata
    automation_data = db.Column(db.Text)  # JSON data about the automation process
    
    def set_automation_data(self, data):
        """Set automation data as JSON"""
        self.automation_data = json.dumps(data)
    
    def get_automation_data(self):
        """Get automation data from JSON"""
        if self.automation_data:
            return json.loads(self.automation_data)
        return {}
    
    def __repr__(self):
        return f'<JobApplication {self.job_title} at {self.company_name}>'

class UserSettings(db.Model):
    """User settings and preferences"""
    __tablename__ = 'user_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Resume and profile
    resume_filename = db.Column(db.String(255))
    resume_file_path = db.Column(db.String(500))
    resume_uploaded_at = db.Column(db.DateTime)
    
    # Job preferences (stored as JSON)
    job_preferences = db.Column(db.Text)  # JSON: keywords, locations, salary range, etc.
    automation_settings = db.Column(db.Text)  # JSON: automation preferences
    
    # Notification preferences
    email_notifications = db.Column(db.Boolean, default=True)
    daily_summary_email = db.Column(db.Boolean, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_job_preferences(self, preferences):
        """Set job preferences as JSON"""
        self.job_preferences = json.dumps(preferences)
    
    def get_job_preferences(self):
        """Get job preferences from JSON"""
        if self.job_preferences:
            return json.loads(self.job_preferences)
        return {}
    
    def set_automation_settings(self, settings):
        """Set automation settings as JSON"""
        self.automation_settings = json.dumps(settings)
    
    def get_automation_settings(self):
        """Get automation settings from JSON"""
        if self.automation_settings:
            return json.loads(self.automation_settings)
        return {}
    
    def __repr__(self):
        return f'<UserSettings for {self.user.email}>'

class AutomationSession(db.Model):
    """Automation session tracking"""
    __tablename__ = 'automation_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Session details
    status = db.Column(db.String(20), default='running')  # running, completed, failed, stopped
    started_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    completed_at = db.Column(db.DateTime)
    
    # Results
    jobs_found = db.Column(db.Integer, default=0)
    applications_submitted = db.Column(db.Integer, default=0)
    applications_failed = db.Column(db.Integer, default=0)
    rate_limited = db.Column(db.Boolean, default=False)
    
    # Error tracking
    error_message = db.Column(db.Text)
    
    # Relationships
    job_applications = db.relationship('JobApplication', backref='automation_session', lazy='dynamic')
    
    def mark_completed(self):
        """Mark session as completed"""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
    
    def mark_failed(self, error_message=None):
        """Mark session as failed"""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        if error_message:
            self.error_message = error_message
    
    def duration_minutes(self):
        """Get session duration in minutes"""
        end_time = self.completed_at or datetime.utcnow()
        delta = end_time - self.started_at
        return round(delta.total_seconds() / 60, 1)
    
    def __repr__(self):
        return f'<AutomationSession {self.id} - {self.status}>'

class SystemLog(db.Model):
    """System logging and monitoring"""
    __tablename__ = 'system_logs'

    id = db.Column(db.Integer, primary_key=True)
    level = db.Column(db.String(20), nullable=False, index=True)  # INFO, WARNING, ERROR, CRITICAL
    message = db.Column(db.Text, nullable=False)
    source = db.Column(db.String(100))  # Source component/module
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Additional context
    session_id = db.Column(db.String(100))  # Session or request ID
    ip_address = db.Column(db.String(45))  # IPv4 or IPv6
    user_agent = db.Column(db.String(500))
    extra_data = db.Column(db.Text)  # JSON string for additional data

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    user = db.relationship('User', backref='system_logs')

    def __repr__(self):
        return f'<SystemLog {self.level}: {self.message[:50]}>'

    def get_extra_data(self):
        """Get extra data as dictionary"""
        if self.extra_data:
            try:
                return json.loads(self.extra_data)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    def set_extra_data(self, data):
        """Set extra data from dictionary"""
        if data:
            try:
                self.extra_data = json.dumps(data)
                return True
            except (TypeError, ValueError) as e:
                current_app.logger.error(f"Error setting extra data: {e}")
                return False
        return True

    @staticmethod
    def log_event(level, message, source=None, user_id=None, session_id=None,
                  ip_address=None, user_agent=None, extra_data=None):
        """Create a new system log entry"""
        try:
            log_entry = SystemLog(
                level=level.upper(),
                message=message,
                source=source,
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent
            )

            if extra_data:
                log_entry.set_extra_data(extra_data)

            db.session.add(log_entry)
            db.session.commit()

            return log_entry

        except Exception as e:
            current_app.logger.error(f"Error creating system log: {e}")
            db.session.rollback()
            return None

    @staticmethod
    def log_user_action(user_id, action, source='user_action', extra_data=None):
        """Log a user action"""
        return SystemLog.log_event(
            level='INFO',
            message=f"User action: {action}",
            source=source,
            user_id=user_id,
            extra_data=extra_data
        )

    @staticmethod
    def log_admin_action(admin_user_id, action, target_user_id=None, extra_data=None):
        """Log an admin action"""
        extra = extra_data or {}
        if target_user_id:
            extra['target_user_id'] = target_user_id

        return SystemLog.log_event(
            level='INFO',
            message=f"Admin action: {action}",
            source='admin_panel',
            user_id=admin_user_id,
            extra_data=extra
        )

    @staticmethod
    def log_automation_event(user_id, session_id, event, level='INFO', extra_data=None):
        """Log an automation-related event"""
        return SystemLog.log_event(
            level=level,
            message=f"Automation event: {event}",
            source='automation',
            user_id=user_id,
            session_id=str(session_id),
            extra_data=extra_data
        )

    @staticmethod
    def log_error(message, source=None, user_id=None, extra_data=None):
        """Log an error event"""
        return SystemLog.log_event(
            level='ERROR',
            message=message,
            source=source,
            user_id=user_id,
            extra_data=extra_data
        )

    @staticmethod
    def cleanup_old_logs(days=30):
        """Clean up logs older than specified days"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            deleted = SystemLog.query.filter(SystemLog.created_at < cutoff_date).delete()
            db.session.commit()

            current_app.logger.info(f"Cleaned up {deleted} old log entries")
            return deleted

        except Exception as e:
            current_app.logger.error(f"Error cleaning up logs: {e}")
            db.session.rollback()
            return 0
