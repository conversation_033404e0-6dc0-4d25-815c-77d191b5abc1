{% extends "base.html" %}

{% block title %}Help Center - AutoApply.co.nz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/marketing.css') }}">
{% endblock %}

{% block content %}
<!-- Help Hero -->
<section class="help-hero py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">Help Center</h1>
                <p class="lead text-muted mb-4">
                    Find answers to common questions and learn how to get the most out of AutoApply.co.nz
                </p>
                
                <!-- Search Box -->
                <div class="help-search mb-4">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" placeholder="Search for help articles..." id="helpSearch">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Links -->
<section class="quick-links py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="quick-link-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="quick-link-icon mb-3">
                        <i class="fas fa-play-circle text-primary fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Getting Started</h5>
                    <p class="text-muted mb-3">
                        Learn how to set up your account and start automating your job applications.
                    </p>
                    <a href="#getting-started" class="btn btn-outline-primary">Learn More</a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="quick-link-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="quick-link-icon mb-3">
                        <i class="fas fa-cogs text-success fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Account Settings</h5>
                    <p class="text-muted mb-3">
                        Manage your profile, preferences, and automation settings.
                    </p>
                    <a href="#account-settings" class="btn btn-outline-success">Learn More</a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="quick-link-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="quick-link-icon mb-3">
                        <i class="fas fa-credit-card text-warning fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Billing & Plans</h5>
                    <p class="text-muted mb-3">
                        Understand pricing, manage subscriptions, and billing information.
                    </p>
                    <a href="#billing" class="btn btn-outline-warning">Learn More</a>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="quick-link-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="quick-link-icon mb-3">
                        <i class="fas fa-question-circle text-info fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Troubleshooting</h5>
                    <p class="text-muted mb-3">
                        Solve common issues and get your automation running smoothly.
                    </p>
                    <a href="#troubleshooting" class="btn btn-outline-info">Learn More</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Sections -->
<section class="help-faq py-5 bg-light">
    <div class="container">
        <!-- Getting Started -->
        <div id="getting-started" class="faq-section mb-5">
            <h2 class="h1 fw-bold mb-4 text-center">Getting Started</h2>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="gettingStartedAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#gs1">
                                    How do I create an account?
                                </button>
                            </h2>
                            <div id="gs1" class="accordion-collapse collapse show" data-bs-parent="#gettingStartedAccordion">
                                <div class="accordion-body">
                                    <p>Creating an account is simple:</p>
                                    <ol>
                                        <li>Click "Start Free Trial" on our homepage</li>
                                        <li>Enter your email address and create a password</li>
                                        <li>Verify your email address</li>
                                        <li>Complete your profile setup</li>
                                        <li>Upload your resume and set your job preferences</li>
                                    </ol>
                                    <p>Your 7-day free trial starts immediately - no credit card required!</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gs2">
                                    How do I connect my Seek account?
                                </button>
                            </h2>
                            <div id="gs2" class="accordion-collapse collapse" data-bs-parent="#gettingStartedAccordion">
                                <div class="accordion-body">
                                    <p>To connect your Seek.co.nz account:</p>
                                    <ol>
                                        <li>Go to your Dashboard and click "Connect Seek Account"</li>
                                        <li>Enter your Seek.co.nz email and password</li>
                                        <li>We'll verify your credentials securely</li>
                                        <li>Your account will be connected and ready for automation</li>
                                    </ol>
                                    <p><strong>Note:</strong> Your login details are encrypted and stored securely. We never share your information with third parties.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gs3">
                                    How do I start my first automation?
                                </button>
                            </h2>
                            <div id="gs3" class="accordion-collapse collapse" data-bs-parent="#gettingStartedAccordion">
                                <div class="accordion-body">
                                    <p>Starting your first automation:</p>
                                    <ol>
                                        <li>Complete your profile and upload your resume</li>
                                        <li>Set your job preferences (location, salary, industry)</li>
                                        <li>Review and customize your cover letter template</li>
                                        <li>Click "Start Automation" on your dashboard</li>
                                        <li>Monitor progress in real-time</li>
                                    </ol>
                                    <p>Your first applications will typically start within 30 minutes!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Settings -->
        <div id="account-settings" class="faq-section mb-5">
            <h2 class="h1 fw-bold mb-4 text-center">Account Settings</h2>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="accountSettingsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#as1">
                                    How do I update my job preferences?
                                </button>
                            </h2>
                            <div id="as1" class="accordion-collapse collapse show" data-bs-parent="#accountSettingsAccordion">
                                <div class="accordion-body">
                                    <p>To update your job preferences:</p>
                                    <ol>
                                        <li>Go to Dashboard → Settings → Job Preferences</li>
                                        <li>Update your desired locations, salary range, and industries</li>
                                        <li>Set your daily application limits</li>
                                        <li>Save your changes</li>
                                    </ol>
                                    <p>Changes take effect immediately for new automation sessions.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#as2">
                                    How do I customize my cover letter?
                                </button>
                            </h2>
                            <div id="as2" class="accordion-collapse collapse" data-bs-parent="#accountSettingsAccordion">
                                <div class="accordion-body">
                                    <p>Customizing your cover letter template:</p>
                                    <ol>
                                        <li>Navigate to Settings → Cover Letter Template</li>
                                        <li>Edit the template using our editor</li>
                                        <li>Use variables like {company_name} and {job_title}</li>
                                        <li>Preview your template</li>
                                        <li>Save your changes</li>
                                    </ol>
                                    <p>Our AI will personalize each cover letter based on the specific job requirements.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Billing -->
        <div id="billing" class="faq-section mb-5">
            <h2 class="h1 fw-bold mb-4 text-center">Billing & Plans</h2>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="billingAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#b1">
                                    How much does AutoApply cost?
                                </button>
                            </h2>
                            <div id="b1" class="accordion-collapse collapse show" data-bs-parent="#billingAccordion">
                                <div class="accordion-body">
                                    <p>Our pricing is simple and transparent:</p>
                                    <ul>
                                        <li><strong>Free Trial:</strong> 7 days, up to 10 applications</li>
                                        <li><strong>Pro Plan:</strong> $10/month for unlimited applications</li>
                                        <li><strong>Enterprise:</strong> Custom pricing for teams</li>
                                    </ul>
                                    <p>All plans include AI cover letters, analytics, and email support. No setup fees or hidden costs.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#b2">
                                    How do I cancel my subscription?
                                </button>
                            </h2>
                            <div id="b2" class="accordion-collapse collapse" data-bs-parent="#billingAccordion">
                                <div class="accordion-body">
                                    <p>You can cancel anytime:</p>
                                    <ol>
                                        <li>Go to Dashboard → Settings → Billing</li>
                                        <li>Click "Cancel Subscription"</li>
                                        <li>Confirm your cancellation</li>
                                    </ol>
                                    <p>You'll retain access until the end of your billing period. No cancellation fees apply.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Troubleshooting -->
        <div id="troubleshooting" class="faq-section">
            <h2 class="h1 fw-bold mb-4 text-center">Troubleshooting</h2>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="troubleshootingAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#t1">
                                    Why aren't applications being sent?
                                </button>
                            </h2>
                            <div id="t1" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <p>If applications aren't being sent, check:</p>
                                    <ul>
                                        <li>Your Seek account is connected and credentials are valid</li>
                                        <li>Your job preferences aren't too restrictive</li>
                                        <li>You haven't reached your daily application limit</li>
                                        <li>Your subscription is active</li>
                                        <li>There are available jobs matching your criteria</li>
                                    </ul>
                                    <p>If issues persist, contact our support team for assistance.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#t2">
                                    What if my Seek account gets locked?
                                </button>
                            </h2>
                            <div id="t2" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <p>Account locks are rare but can happen. If your Seek account is locked:</p>
                                    <ol>
                                        <li>Contact Seek customer support to unlock your account</li>
                                        <li>Pause your AutoApply automation temporarily</li>
                                        <li>Once unlocked, reconnect your account in AutoApply</li>
                                        <li>Consider reducing your daily application limits</li>
                                    </ol>
                                    <p>Our built-in safeguards minimize this risk, but we're here to help if it occurs.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Support -->
<section class="help-contact py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-4">Still Need Help?</h2>
                <p class="lead text-muted mb-4">
                    Can't find what you're looking for? Our support team is here to help.
                </p>
                
                <div class="row g-3 justify-content-center">
                    <div class="col-md-4">
                        <a href="{{ url_for('main.contact') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-paper-plane me-2"></i>Email Us
                        </a>
                    </div>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-2"></i>
                        Average response time: 4 hours during business hours
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
// Help search functionality
document.getElementById('helpSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const accordionItems = document.querySelectorAll('.accordion-item');
    
    accordionItems.forEach(item => {
        const button = item.querySelector('.accordion-button');
        const body = item.querySelector('.accordion-body');
        const text = (button.textContent + ' ' + body.textContent).toLowerCase();
        
        if (text.includes(searchTerm) || searchTerm === '') {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Track help interactions
document.querySelectorAll('.quick-link-card a, .accordion-button').forEach(element => {
    element.addEventListener('click', function() {
        const section = this.closest('.faq-section')?.id || 'quick-links';
        const item = this.textContent.trim();
        
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                'event_category': 'Help',
                'event_label': `${section}: ${item}`,
                'value': 1
            });
        }
    });
});
</script>
{% endblock %}
