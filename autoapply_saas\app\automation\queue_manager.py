"""
Queue management system for AutoApply.co.nz
Handles concurrent automation sessions and resource management
"""

import time
from datetime import datetime, timedelta
from flask import current_app
from app import db, celery
from app.models import User, AutomationSession
from app.automation.tasks import run_job_automation

class QueueManager:
    """Manages automation job queue and concurrent sessions"""
    
    def __init__(self):
        self.max_concurrent_sessions = current_app.config.get('MAX_CONCURRENT_AUTOMATIONS', 5)
    
    def can_start_automation(self, user_id):
        """
        Check if user can start a new automation session
        
        Args:
            user_id: User ID
            
        Returns:
            tuple: (can_start: bool, reason: str)
        """
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "User not found"
            
            # Check if user can apply to jobs (subscription/trial)
            if not user.can_apply_to_jobs():
                return False, "Active subscription or trial required"
            
            # Check for existing running session
            running_session = user.automation_sessions.filter_by(status='running').first()
            if running_session:
                return False, "Automation session already running"
            
            # Check daily application limit
            applications_today = user.get_applications_today()
            max_daily = current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50)
            
            if applications_today >= max_daily:
                return False, f"Daily application limit of {max_daily} reached"
            
            # Check global concurrent session limit
            active_sessions = AutomationSession.query.filter_by(status='running').count()
            if active_sessions >= self.max_concurrent_sessions:
                return False, f"System at capacity ({self.max_concurrent_sessions} concurrent sessions)"
            
            return True, "Can start automation"
            
        except Exception as e:
            current_app.logger.error(f"Error checking automation eligibility: {e}")
            return False, "System error"
    
    def queue_automation_job(self, user_id, job_preferences=None, priority='normal'):
        """
        Queue an automation job for execution
        
        Args:
            user_id: User ID
            job_preferences: Optional job preferences
            priority: Job priority ('high', 'normal', 'low')
            
        Returns:
            dict: Result with task_id or error
        """
        try:
            # Check if user can start automation
            can_start, reason = self.can_start_automation(user_id)
            if not can_start:
                return {'success': False, 'error': reason}
            
            # Queue the job with appropriate priority
            queue_name = self._get_queue_name(priority)
            
            task = run_job_automation.apply_async(
                args=[user_id, job_preferences],
                queue=queue_name,
                retry=False
            )
            
            current_app.logger.info(f"Queued automation job for user {user_id} with task ID {task.id}")
            
            return {
                'success': True,
                'task_id': task.id,
                'queue': queue_name,
                'estimated_wait_time': self._estimate_wait_time(queue_name)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error queueing automation job: {e}")
            return {'success': False, 'error': 'Failed to queue job'}
    
    def get_queue_status(self):
        """
        Get current queue status and statistics
        
        Returns:
            dict: Queue status information
        """
        try:
            # Get active sessions
            active_sessions = AutomationSession.query.filter_by(status='running').all()
            
            # Get queue lengths from Celery
            inspect = celery.control.inspect()
            active_tasks = inspect.active() or {}
            reserved_tasks = inspect.reserved() or {}
            
            # Calculate queue statistics
            total_active = sum(len(tasks) for tasks in active_tasks.values())
            total_reserved = sum(len(tasks) for tasks in reserved_tasks.values())
            
            # Get recent session statistics
            hour_ago = datetime.utcnow() - timedelta(hours=1)
            recent_sessions = AutomationSession.query.filter(
                AutomationSession.started_at >= hour_ago
            ).all()
            
            completed_recent = len([s for s in recent_sessions if s.status == 'completed'])
            failed_recent = len([s for s in recent_sessions if s.status == 'failed'])
            
            return {
                'active_sessions': len(active_sessions),
                'max_concurrent_sessions': self.max_concurrent_sessions,
                'available_slots': max(0, self.max_concurrent_sessions - len(active_sessions)),
                'queue_length': total_reserved,
                'processing_tasks': total_active,
                'recent_completed': completed_recent,
                'recent_failed': failed_recent,
                'success_rate_recent': (completed_recent / len(recent_sessions) * 100) if recent_sessions else 0,
                'system_healthy': self._is_system_healthy()
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting queue status: {e}")
            return {'error': 'Unable to get queue status'}
    
    def get_user_queue_position(self, user_id, task_id=None):
        """
        Get user's position in the automation queue
        
        Args:
            user_id: User ID
            task_id: Optional task ID to check specific task
            
        Returns:
            dict: Queue position information
        """
        try:
            # Check if user has running session
            running_session = AutomationSession.query.filter_by(
                user_id=user_id, 
                status='running'
            ).first()
            
            if running_session:
                return {
                    'status': 'running',
                    'session_id': running_session.id,
                    'started_at': running_session.started_at.isoformat(),
                    'duration_minutes': running_session.duration_minutes()
                }
            
            # Check queue position if task_id provided
            if task_id:
                task = run_job_automation.AsyncResult(task_id)
                
                if task.state == 'PENDING':
                    # Estimate queue position (simplified)
                    queue_status = self.get_queue_status()
                    estimated_position = queue_status.get('queue_length', 0)
                    estimated_wait = self._estimate_wait_time('default')
                    
                    return {
                        'status': 'queued',
                        'task_id': task_id,
                        'estimated_position': estimated_position,
                        'estimated_wait_minutes': estimated_wait
                    }
                elif task.state == 'PROGRESS':
                    return {
                        'status': 'starting',
                        'task_id': task_id,
                        'info': task.info
                    }
                else:
                    return {
                        'status': task.state.lower(),
                        'task_id': task_id
                    }
            
            return {'status': 'not_queued'}
            
        except Exception as e:
            current_app.logger.error(f"Error getting user queue position: {e}")
            return {'error': 'Unable to get queue position'}
    
    def cancel_user_automation(self, user_id, task_id=None):
        """
        Cancel user's automation session or queued job
        
        Args:
            user_id: User ID
            task_id: Optional task ID to cancel specific task
            
        Returns:
            dict: Cancellation result
        """
        try:
            # Cancel running session
            running_session = AutomationSession.query.filter_by(
                user_id=user_id, 
                status='running'
            ).first()
            
            if running_session:
                running_session.status = 'cancelled'
                running_session.completed_at = datetime.utcnow()
                running_session.error_message = 'Cancelled by user'
                db.session.commit()
                
                current_app.logger.info(f"Cancelled running session {running_session.id} for user {user_id}")
                
                return {
                    'success': True,
                    'message': 'Running automation session cancelled',
                    'session_id': running_session.id
                }
            
            # Cancel queued task
            if task_id:
                task = run_job_automation.AsyncResult(task_id)
                if task.state == 'PENDING':
                    task.revoke(terminate=True)
                    
                    current_app.logger.info(f"Cancelled queued task {task_id} for user {user_id}")
                    
                    return {
                        'success': True,
                        'message': 'Queued automation job cancelled',
                        'task_id': task_id
                    }
            
            return {
                'success': False,
                'error': 'No active automation session or queued job found'
            }
            
        except Exception as e:
            current_app.logger.error(f"Error cancelling automation: {e}")
            return {'success': False, 'error': 'Failed to cancel automation'}
    
    def _get_queue_name(self, priority):
        """Get queue name based on priority"""
        queue_mapping = {
            'high': 'high_priority',
            'normal': 'default',
            'low': 'low_priority'
        }
        return queue_mapping.get(priority, 'default')
    
    def _estimate_wait_time(self, queue_name):
        """
        Estimate wait time for queue
        
        Args:
            queue_name: Name of the queue
            
        Returns:
            int: Estimated wait time in minutes
        """
        try:
            # Get average session duration from recent completed sessions
            recent_sessions = AutomationSession.query.filter(
                AutomationSession.status == 'completed',
                AutomationSession.completed_at >= datetime.utcnow() - timedelta(hours=24)
            ).all()
            
            if recent_sessions:
                avg_duration = sum(s.duration_minutes() for s in recent_sessions) / len(recent_sessions)
            else:
                avg_duration = 15  # Default estimate
            
            # Get current queue length
            queue_status = self.get_queue_status()
            queue_length = queue_status.get('queue_length', 0)
            available_slots = queue_status.get('available_slots', 1)
            
            # Estimate wait time
            if available_slots > 0:
                return 0  # Can start immediately
            else:
                return int((queue_length / self.max_concurrent_sessions) * avg_duration)
                
        except Exception as e:
            current_app.logger.error(f"Error estimating wait time: {e}")
            return 10  # Default estimate
    
    def _is_system_healthy(self):
        """Check if the automation system is healthy"""
        try:
            # Check if Celery workers are responding
            inspect = celery.control.inspect()
            stats = inspect.stats()
            
            if not stats:
                return False
            
            # Check if there are available workers
            active_workers = len(stats)
            if active_workers == 0:
                return False
            
            # Check recent failure rate
            hour_ago = datetime.utcnow() - timedelta(hours=1)
            recent_sessions = AutomationSession.query.filter(
                AutomationSession.started_at >= hour_ago
            ).all()
            
            if recent_sessions:
                failed_sessions = [s for s in recent_sessions if s.status == 'failed']
                failure_rate = len(failed_sessions) / len(recent_sessions)
                
                # Consider unhealthy if failure rate > 50%
                if failure_rate > 0.5:
                    return False
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error checking system health: {e}")
            return False
