"""
Authentication forms for AutoApply.co.nz
"""

from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User
import re

class LoginForm(FlaskForm):
    """User login form"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = <PERSON><PERSON>anField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    """User registration form"""
    first_name = StringField('First Name', validators=[
        DataRequired(), 
        Length(min=2, max=50, message='First name must be between 2 and 50 characters')
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(), 
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters')
    ])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(), 
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Create Account')
    
    def validate_email(self, email):
        """Validate email is from Seek.co.nz domain and not already registered"""
        # Check if email is from seek.co.nz domain
        if not email.data.lower().endswith('@seek.co.nz'):
            raise ValidationError('You must use a Seek.co.nz email address to register.')
        
        # Check if email is already registered
        user = User.query.filter_by(email=email.data.lower()).first()
        if user:
            raise ValidationError('This email address is already registered. Please use a different email or sign in.')
    
    def validate_password(self, password):
        """Validate password strength"""
        password_value = password.data
        
        # Check for minimum requirements
        if len(password_value) < 8:
            raise ValidationError('Password must be at least 8 characters long.')
        
        # Check for at least one uppercase letter
        if not re.search(r'[A-Z]', password_value):
            raise ValidationError('Password must contain at least one uppercase letter.')
        
        # Check for at least one lowercase letter
        if not re.search(r'[a-z]', password_value):
            raise ValidationError('Password must contain at least one lowercase letter.')
        
        # Check for at least one digit
        if not re.search(r'\d', password_value):
            raise ValidationError('Password must contain at least one number.')

class RequestPasswordResetForm(FlaskForm):
    """Password reset request form"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    submit = SubmitField('Request Password Reset')
    
    def validate_email(self, email):
        """Validate email exists and is from Seek.co.nz"""
        if not email.data.lower().endswith('@seek.co.nz'):
            raise ValidationError('You must use a Seek.co.nz email address.')
        
        user = User.query.filter_by(email=email.data.lower()).first()
        if not user:
            raise ValidationError('No account found with this email address.')

class ResetPasswordForm(FlaskForm):
    """Password reset form"""
    password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(), 
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Reset Password')
    
    def validate_password(self, password):
        """Validate password strength"""
        password_value = password.data
        
        # Check for minimum requirements
        if len(password_value) < 8:
            raise ValidationError('Password must be at least 8 characters long.')
        
        # Check for at least one uppercase letter
        if not re.search(r'[A-Z]', password_value):
            raise ValidationError('Password must contain at least one uppercase letter.')
        
        # Check for at least one lowercase letter
        if not re.search(r'[a-z]', password_value):
            raise ValidationError('Password must contain at least one lowercase letter.')
        
        # Check for at least one digit
        if not re.search(r'\d', password_value):
            raise ValidationError('Password must contain at least one number.')

class ChangePasswordForm(FlaskForm):
    """Change password form for logged-in users"""
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    password = PasswordField('New Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    password2 = PasswordField('Repeat New Password', validators=[
        DataRequired(), 
        EqualTo('password', message='Passwords must match')
    ])
    submit = SubmitField('Change Password')
    
    def validate_password(self, password):
        """Validate password strength"""
        password_value = password.data
        
        # Check for minimum requirements
        if len(password_value) < 8:
            raise ValidationError('Password must be at least 8 characters long.')
        
        # Check for at least one uppercase letter
        if not re.search(r'[A-Z]', password_value):
            raise ValidationError('Password must contain at least one uppercase letter.')
        
        # Check for at least one lowercase letter
        if not re.search(r'[a-z]', password_value):
            raise ValidationError('Password must contain at least one lowercase letter.')
        
        # Check for at least one digit
        if not re.search(r'\d', password_value):
            raise ValidationError('Password must contain at least one number.')

class EmailVerificationForm(FlaskForm):
    """Email verification form"""
    submit = SubmitField('Resend Verification Email')
