# Core Flask and web framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-Login==0.6.3
Flask-Mail==0.9.1
Flask-WTF==1.1.1
WTForms==3.0.1

# Database
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21

# Background tasks
Celery==5.3.2
redis==5.0.0

# Payment processing
stripe==6.6.0

# Authentication and security
PyJWT==2.8.0
Werkzeug==2.3.7
bcrypt==4.0.1

# Web automation (existing functionality)
selenium==4.15.0
undetected-chromedriver==3.5.3
requests==2.31.0

# AI and document processing (existing functionality)
openai==0.28.1
PyPDF2==3.0.1

# Utilities
python-dotenv==1.0.0
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3

# Development and testing
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
factory-boy==3.3.0

# Monitoring and logging
sentry-sdk[flask]==1.32.0

# Email
sendgrid==6.10.0

# File handling
Pillow==10.0.1

# Rate limiting
Flask-Limiter==3.5.0

# Environment and configuration
gunicorn==21.2.0
python-decouple==3.8

# Date and time utilities
python-dateutil==2.8.2

# HTTP client
httpx==0.25.0

# Data validation
marshmallow==3.20.1

# Caching
Flask-Caching==2.1.0

# CORS support
Flask-CORS==4.0.0

# Admin interface
Flask-Admin==1.6.1

# API documentation
flask-restx==1.2.0

# Timezone handling
pytz==2023.3
