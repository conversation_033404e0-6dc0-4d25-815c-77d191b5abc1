"""
Main routes for AutoApply.co.nz marketing website
"""

from flask import render_template, redirect, url_for, request, flash, current_app
from flask_login import current_user
from app.main import bp
from app.models import User, JobApplication, AutomationSession
from app import db
from datetime import datetime, timedelta

@bp.route('/')
def index():
    """Landing page"""
    # Get some statistics for the landing page
    stats = {
        'total_users': User.query.filter_by(is_active=True).count(),
        'total_applications': JobApplication.query.count(),
        'successful_sessions': AutomationSession.query.filter_by(status='completed').count()
    }
    
    return render_template('main/index.html', title='AutoApply.co.nz - Automate Your Job Applications', stats=stats)

@bp.route('/features')
def features():
    """Features page"""
    return render_template('main/features.html', title='Features - AutoApply.co.nz')

@bp.route('/pricing')
def pricing():
    """Pricing page"""
    return render_template('main/pricing.html', title='Pricing - AutoApply.co.nz')

@bp.route('/how-it-works')
def how_it_works():
    """How it works page"""
    return render_template('main/how_it_works.html', title='How It Works - AutoApply.co.nz')

@bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('main/privacy.html', title='Privacy Policy - AutoApply.co.nz')

@bp.route('/terms')
def terms():
    """Terms of service page"""
    return render_template('main/terms.html', title='Terms of Service - AutoApply.co.nz')

@bp.route('/contact')
def contact():
    """Contact page"""
    return render_template('main/contact.html', title='Contact Us - AutoApply.co.nz')

@bp.route('/about')
def about():
    """About page"""
    return render_template('main/about.html', title='About - AutoApply.co.nz')

@bp.route('/get-started')
def get_started():
    """Get started page - redirects to registration"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    return redirect(url_for('auth.register'))

@bp.route('/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        
        return {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        }, 200
        
    except Exception as e:
        current_app.logger.error(f"Health check failed: {e}")
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }, 500

@bp.route('/robots.txt')
def robots_txt():
    """Robots.txt for SEO"""
    return """User-agent: *
Allow: /
Disallow: /dashboard/
Disallow: /admin/
Disallow: /api/

Sitemap: https://autoapply.co.nz/sitemap.xml
""", 200, {'Content-Type': 'text/plain'}

@bp.route('/sitemap.xml')
def sitemap_xml():
    """Basic sitemap for SEO"""
    pages = [
        {'url': url_for('main.index', _external=True), 'priority': '1.0'},
        {'url': url_for('main.features', _external=True), 'priority': '0.8'},
        {'url': url_for('main.pricing', _external=True), 'priority': '0.9'},
        {'url': url_for('main.how_it_works', _external=True), 'priority': '0.8'},
        {'url': url_for('main.about', _external=True), 'priority': '0.6'},
        {'url': url_for('main.contact', _external=True), 'priority': '0.6'},
        {'url': url_for('auth.register', _external=True), 'priority': '0.9'},
        {'url': url_for('auth.login', _external=True), 'priority': '0.7'},
    ]
    
    sitemap_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    sitemap_xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
    
    for page in pages:
        sitemap_xml += f'  <url>\n'
        sitemap_xml += f'    <loc>{page["url"]}</loc>\n'
        sitemap_xml += f'    <priority>{page["priority"]}</priority>\n'
        sitemap_xml += f'    <changefreq>weekly</changefreq>\n'
        sitemap_xml += f'  </url>\n'
    
    sitemap_xml += '</urlset>'
    
    return sitemap_xml, 200, {'Content-Type': 'application/xml'}
