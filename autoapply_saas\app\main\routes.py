"""
Main routes for AutoApply.co.nz marketing website
"""

from flask import render_template, redirect, url_for, request, flash, current_app, jsonify
from flask_login import current_user
from app.main import bp
from app.models import User, JobApplication, AutomationSession
from app import db, mail
from flask_mail import Message
from datetime import datetime, timedelta

@bp.route('/')
def index():
    """Landing page"""
    # Get some statistics for the landing page
    stats = {
        'total_users': User.query.filter_by(is_active=True).count(),
        'total_applications': JobApplication.query.count(),
        'successful_sessions': AutomationSession.query.filter_by(status='completed').count()
    }
    
    return render_template('marketing/index.html', title='AutoApply.co.nz - Automate Your Job Applications', stats=stats)

@bp.route('/features')
def features():
    """Features page"""
    return render_template('marketing/features.html', title='Features - AutoApply.co.nz')

@bp.route('/pricing')
def pricing():
    """Pricing page"""
    return render_template('marketing/pricing.html', title='Pricing - AutoApply.co.nz')

@bp.route('/how-it-works')
def how_it_works():
    """How it works page"""
    return render_template('main/how_it_works.html', title='How It Works - AutoApply.co.nz')

@bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('main/privacy.html', title='Privacy Policy - AutoApply.co.nz')

@bp.route('/terms')
def terms():
    """Terms of service page"""
    return render_template('main/terms.html', title='Terms of Service - AutoApply.co.nz')

@bp.route('/contact', methods=['GET', 'POST'])
def contact():
    """Contact page with form handling"""
    if request.method == 'POST':
        try:
            # Get form data
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            subject = request.form.get('subject', '').strip()
            message = request.form.get('message', '').strip()
            newsletter = request.form.get('newsletter') == 'on'

            # Validate required fields
            if not all([first_name, last_name, email, subject, message]):
                return jsonify({'success': False, 'error': 'Please fill in all required fields'}), 400

            # Send email to support team
            msg = Message(
                subject=f'Contact Form: {subject}',
                sender=current_app.config['MAIL_USERNAME'],
                recipients=['<EMAIL>']
            )

            msg.body = f"""
New contact form submission:

Name: {first_name} {last_name}
Email: {email}
Phone: {phone}
Subject: {subject}
Newsletter: {'Yes' if newsletter else 'No'}

Message:
{message}
"""

            mail.send(msg)

            # Send confirmation email to user
            confirmation_msg = Message(
                subject='Thank you for contacting AutoApply.co.nz',
                sender=current_app.config['MAIL_USERNAME'],
                recipients=[email]
            )

            confirmation_msg.body = f"""
Hi {first_name},

Thank you for contacting AutoApply.co.nz. We've received your message and will get back to you within 24 hours.

Your message:
Subject: {subject}
Message: {message}

Best regards,
The AutoApply Team
"""

            mail.send(confirmation_msg)

            return jsonify({'success': True, 'message': 'Message sent successfully!'})

        except Exception as e:
            current_app.logger.error(f"Contact form error: {e}")
            return jsonify({'success': False, 'error': 'Failed to send message. Please try again.'}), 500

    return render_template('marketing/contact.html', title='Contact Us - AutoApply.co.nz')

@bp.route('/about')
def about():
    """About page"""
    return render_template('marketing/about.html', title='About - AutoApply.co.nz')

@bp.route('/help')
def help():
    """Help center page"""
    return render_template('marketing/help.html', title='Help Center - AutoApply.co.nz')

@bp.route('/get-started')
def get_started():
    """Get started page - redirects to registration"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    return redirect(url_for('auth.register'))

@bp.route('/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        
        return {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        }, 200
        
    except Exception as e:
        current_app.logger.error(f"Health check failed: {e}")
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }, 500

@bp.route('/robots.txt')
def robots_txt():
    """Robots.txt for SEO"""
    return """User-agent: *
Allow: /
Disallow: /dashboard/
Disallow: /admin/
Disallow: /api/

Sitemap: https://autoapply.co.nz/sitemap.xml
""", 200, {'Content-Type': 'text/plain'}

@bp.route('/sitemap.xml')
def sitemap_xml():
    """Basic sitemap for SEO"""
    pages = [
        {'url': url_for('main.index', _external=True), 'priority': '1.0'},
        {'url': url_for('main.features', _external=True), 'priority': '0.8'},
        {'url': url_for('main.pricing', _external=True), 'priority': '0.9'},
        {'url': url_for('main.how_it_works', _external=True), 'priority': '0.8'},
        {'url': url_for('main.about', _external=True), 'priority': '0.6'},
        {'url': url_for('main.contact', _external=True), 'priority': '0.6'},
        {'url': url_for('auth.register', _external=True), 'priority': '0.9'},
        {'url': url_for('auth.login', _external=True), 'priority': '0.7'},
    ]
    
    sitemap_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    sitemap_xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
    
    for page in pages:
        sitemap_xml += f'  <url>\n'
        sitemap_xml += f'    <loc>{page["url"]}</loc>\n'
        sitemap_xml += f'    <priority>{page["priority"]}</priority>\n'
        sitemap_xml += f'    <changefreq>weekly</changefreq>\n'
        sitemap_xml += f'  </url>\n'
    
    sitemap_xml += '</urlset>'
    
    return sitemap_xml, 200, {'Content-Type': 'application/xml'}
