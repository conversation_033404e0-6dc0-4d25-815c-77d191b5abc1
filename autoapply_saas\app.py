"""
AutoApply.co.nz SaaS Application Entry Point
"""

import os
from flask import Flask
from app import create_app, db, make_celery
from app.models import User, Subscription, JobApplication, UserSettings, AutomationSession, SystemLog
from config import Config

# Create Flask app
app = create_app()

# Create Celery instance
celery = make_celery(app)

@app.shell_context_processor
def make_shell_context():
    """Make database models available in Flask shell"""
    return {
        'db': db,
        'User': User,
        'Subscription': Subscription,
        'JobApplication': JobApplication,
        'UserSettings': UserSettings,
        'AutomationSession': AutomationSession,
        'SystemLog': SystemLog
    }

@app.cli.command()
def init_db():
    """Initialize the database"""
    db.create_all()
    print("Database initialized!")

@app.cli.command()
def create_admin():
    """Create an admin user"""
    from getpass import getpass
    
    email = input("Admin email: ")
    if not email.endswith('@seek.co.nz'):
        print("Admin email must be from seek.co.nz domain")
        return
    
    password = getpass("Admin password: ")
    first_name = input("First name: ")
    last_name = input("Last name: ")
    
    # Check if user already exists
    if User.query.filter_by(email=email).first():
        print("User already exists!")
        return
    
    # Create admin user
    admin = User(
        email=email,
        first_name=first_name,
        last_name=last_name,
        is_verified=True,
        is_active=True
    )
    admin.set_password(password)
    
    db.session.add(admin)
    
    # Create user settings
    settings = UserSettings(user_id=admin.id)
    db.session.add(settings)
    
    db.session.commit()
    
    print(f"Admin user {email} created successfully!")

@app.cli.command()
def test_automation():
    """Test automation system"""
    from app.automation.tasks import test_automation_health
    
    result = test_automation_health.delay()
    print(f"Health check task started: {result.id}")
    
    # Wait for result
    try:
        health_result = result.get(timeout=60)
        print(f"Health check result: {health_result}")
    except Exception as e:
        print(f"Health check failed: {e}")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
