"""
Selenium WebDriver setup and management for AutoApply.co.nz
Refactored from original seek_auto_apply.py
"""

import os
import random
import time
import ssl
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from undetected_chromedriver import Chrome
from flask import current_app

class SeekWebDriver:
    """Selenium WebDriver wrapper for Seek.co.nz automation"""
    
    def __init__(self, user_id, headless=True):
        self.user_id = user_id
        self.headless = headless
        self.driver = None
        self.chrome_profile_dir = None
        self.chrome_cache_dir = None
    
    def setup_directories(self):
        """Create persistent directories for Chrome profile and cache data"""
        try:
            # Create user-specific directories
            base_dir = current_app.config.get('UPLOAD_FOLDER', '/app/uploads')
            chrome_data_dir = os.path.join(base_dir, 'chrome_data')
            
            self.chrome_profile_dir = os.path.join(chrome_data_dir, f"profile_{self.user_id}")
            self.chrome_cache_dir = os.path.join(chrome_data_dir, f"cache_{self.user_id}")
            
            # Create directories if they don't exist
            os.makedirs(self.chrome_profile_dir, exist_ok=True)
            os.makedirs(self.chrome_cache_dir, exist_ok=True)
            
            current_app.logger.info(f"Chrome profile directory: {self.chrome_profile_dir}")
            current_app.logger.info(f"Chrome cache directory: {self.chrome_cache_dir}")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to create persistent directories: {e}")
            return False
    
    def get_random_user_agent(self):
        """Returns a random user agent string to avoid detection"""
        desktop_user_agents = [
            # Windows Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            
            # Windows Firefox
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
            
            # macOS Chrome
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            
            # macOS Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15"
        ]
        selected_agent = random.choice(desktop_user_agents)
        current_app.logger.info(f"Using User Agent: {selected_agent}")
        return selected_agent
    
    def setup_chrome_options(self):
        """Setup Chrome options for automation"""
        options = Options()
        
        # Persistent profile options for login session persistence
        if self.chrome_profile_dir:
            options.add_argument(f'--user-data-dir={self.chrome_profile_dir}')
            options.add_argument(f"--profile-directory=SeekAutoApplyProfile_{self.user_id}")
        
        # Performance options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")
        
        if self.chrome_cache_dir:
            options.add_argument(f'--disk-cache-dir={self.chrome_cache_dir}')
        
        # User agent options
        options.add_argument(f'--user-agent="{self.get_random_user_agent()}"')
        
        # Window options
        options.add_argument("--start-maximized")
        options.add_argument("--window-size=1920,1080")
        
        # Headless mode option
        if self.headless:
            options.add_argument("--headless")
        
        # Additional options for better automation
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        
        return options
    
    def start_driver(self):
        """Start the Chrome WebDriver"""
        try:
            # Add SSL certificate fix
            ssl._create_default_https_context = ssl._create_unverified_context
            
            # Setup directories
            if not self.setup_directories():
                raise Exception("Failed to setup Chrome directories")
            
            # Setup Chrome options
            options = self.setup_chrome_options()
            
            current_app.logger.info(f"Setting up Chrome WebDriver for user {self.user_id}")
            
            # Use Chrome binary path from config if available
            chrome_binary = current_app.config.get('CHROME_BINARY_PATH')
            if chrome_binary and os.path.exists(chrome_binary):
                options.binary_location = chrome_binary
            
            # Create driver
            self.driver = Chrome(options=options)
            
            # Execute script to remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            current_app.logger.info(f"Chrome WebDriver started successfully for user {self.user_id}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to start Chrome WebDriver: {e}")
            return False
    
    def navigate_to_seek(self):
        """Navigate to Seek.co.nz login page"""
        try:
            seek_url = "https://www.seek.co.nz/oauth/login?returnUrl=http%3A%2F%2Fwww.seek.co.nz%2F"
            current_app.logger.info(f"Navigating to {seek_url}")
            
            self.driver.get(seek_url)
            self.driver.implicitly_wait(10)
            time.sleep(random.uniform(3.0, 5.0))
            
            current_app.logger.info("Successfully loaded Seek.co.nz homepage")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to navigate to Seek.co.nz: {e}")
            return False
    
    def wait_for_element(self, by, value, timeout=10):
        """Wait for element to be present and clickable"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            return None
    
    def find_element_safe(self, by, value):
        """Safely find element without throwing exception"""
        try:
            return self.driver.find_element(by, value)
        except NoSuchElementException:
            return None
    
    def cleanup(self):
        """Clean up the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                current_app.logger.info(f"WebDriver cleaned up for user {self.user_id}")
            except Exception as e:
                current_app.logger.error(f"Error cleaning up WebDriver: {e}")
            finally:
                self.driver = None
    
    def __enter__(self):
        """Context manager entry"""
        if self.start_driver():
            return self
        else:
            raise Exception("Failed to start WebDriver")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()
