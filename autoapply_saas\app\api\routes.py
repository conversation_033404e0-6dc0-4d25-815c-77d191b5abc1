"""
API routes for AutoApply.co.nz
REST API endpoints for dashboard and mobile app integration
"""

from datetime import datetime, timedelta
from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.api import bp
from app.models import User, JobApplication, AutomationSession, UserSettings
from app.automation.tasks import run_job_automation
from app.dashboard.routes import get_user_statistics

@bp.route('/user/profile', methods=['GET'])
@login_required
def get_user_profile():
    """Get user profile information"""
    try:
        user_data = {
            'id': current_user.id,
            'email': current_user.email,
            'first_name': current_user.first_name,
            'last_name': current_user.last_name,
            'is_verified': current_user.is_verified,
            'created_at': current_user.created_at.isoformat(),
            'last_login_at': current_user.last_login_at.isoformat() if current_user.last_login_at else None
        }
        
        return jsonify(user_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting user profile: {e}")
        return jsonify({'error': 'Unable to get user profile'}), 500

@bp.route('/user/profile', methods=['PUT'])
@login_required
def update_user_profile():
    """Update user profile information"""
    try:
        data = request.get_json()
        
        # Update allowed fields
        if 'first_name' in data:
            current_user.first_name = data['first_name'].strip()
        
        if 'last_name' in data:
            current_user.last_name = data['last_name'].strip()
        
        current_user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Profile updated successfully'})
        
    except Exception as e:
        current_app.logger.error(f"Error updating user profile: {e}")
        return jsonify({'error': 'Unable to update profile'}), 500

@bp.route('/dashboard/statistics', methods=['GET'])
@login_required
def get_dashboard_statistics():
    """Get comprehensive dashboard statistics"""
    try:
        stats = get_user_statistics(current_user.id)
        return jsonify(stats)
        
    except Exception as e:
        current_app.logger.error(f"Error getting dashboard statistics: {e}")
        return jsonify({'error': 'Unable to get statistics'}), 500

@bp.route('/applications', methods=['GET'])
@login_required
def get_applications():
    """Get user's job applications with pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # Optional filters
        status_filter = request.args.get('status')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Build query
        query = current_user.job_applications
        
        if status_filter:
            query = query.filter(JobApplication.application_status == status_filter)
        
        if date_from:
            try:
                date_from_obj = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                query = query.filter(JobApplication.applied_at >= date_from_obj)
            except ValueError:
                return jsonify({'error': 'Invalid date_from format'}), 400
        
        if date_to:
            try:
                date_to_obj = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                query = query.filter(JobApplication.applied_at <= date_to_obj)
            except ValueError:
                return jsonify({'error': 'Invalid date_to format'}), 400
        
        # Execute query with pagination
        applications = query.order_by(JobApplication.applied_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Format response
        applications_data = []
        for app in applications.items:
            app_data = {
                'id': app.id,
                'job_title': app.job_title,
                'company_name': app.company_name,
                'job_url': app.job_url,
                'application_status': app.application_status,
                'applied_at': app.applied_at.isoformat(),
                'cover_letter_generated': app.cover_letter_generated,
                'automation_session_id': app.automation_session_id
            }
            applications_data.append(app_data)
        
        return jsonify({
            'applications': applications_data,
            'pagination': {
                'page': applications.page,
                'pages': applications.pages,
                'per_page': applications.per_page,
                'total': applications.total,
                'has_next': applications.has_next,
                'has_prev': applications.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting applications: {e}")
        return jsonify({'error': 'Unable to get applications'}), 500

@bp.route('/applications/<int:application_id>', methods=['GET'])
@login_required
def get_application_details(application_id):
    """Get detailed information about a specific application"""
    try:
        application = current_user.job_applications.filter_by(id=application_id).first()
        
        if not application:
            return jsonify({'error': 'Application not found'}), 404
        
        app_data = {
            'id': application.id,
            'job_title': application.job_title,
            'company_name': application.company_name,
            'job_url': application.job_url,
            'job_description': application.job_description,
            'application_status': application.application_status,
            'applied_at': application.applied_at.isoformat(),
            'cover_letter_generated': application.cover_letter_generated,
            'cover_letter_content': application.cover_letter_content,
            'automation_session_id': application.automation_session_id,
            'automation_data': application.get_automation_data()
        }
        
        return jsonify(app_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting application details: {e}")
        return jsonify({'error': 'Unable to get application details'}), 500

@bp.route('/automation/sessions', methods=['GET'])
@login_required
def get_automation_sessions():
    """Get user's automation sessions"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        sessions = current_user.automation_sessions.order_by(
            AutomationSession.started_at.desc()
        ).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        sessions_data = []
        for session in sessions.items:
            session_data = {
                'id': session.id,
                'status': session.status,
                'started_at': session.started_at.isoformat(),
                'completed_at': session.completed_at.isoformat() if session.completed_at else None,
                'jobs_found': session.jobs_found,
                'applications_submitted': session.applications_submitted,
                'applications_failed': session.applications_failed,
                'rate_limited': session.rate_limited,
                'duration_minutes': session.duration_minutes(),
                'error_message': session.error_message
            }
            sessions_data.append(session_data)
        
        return jsonify({
            'sessions': sessions_data,
            'pagination': {
                'page': sessions.page,
                'pages': sessions.pages,
                'per_page': sessions.per_page,
                'total': sessions.total,
                'has_next': sessions.has_next,
                'has_prev': sessions.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error getting automation sessions: {e}")
        return jsonify({'error': 'Unable to get automation sessions'}), 500

@bp.route('/automation/start', methods=['POST'])
@login_required
def start_automation_api():
    """Start automation session via API"""
    try:
        # Check if user can run automation
        if not current_user.can_apply_to_jobs():
            return jsonify({'error': 'Subscription or trial required'}), 403
        
        # Check for existing running session
        running_session = current_user.automation_sessions.filter_by(status='running').first()
        if running_session:
            return jsonify({'error': 'Automation session already running'}), 400
        
        # Check daily application limit
        applications_today = current_user.get_applications_today()
        max_daily = current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50)
        
        if applications_today >= max_daily:
            return jsonify({'error': f'Daily application limit of {max_daily} reached'}), 400
        
        # Get job preferences from request
        data = request.get_json() or {}
        job_preferences = data.get('preferences', {})
        
        # Start automation task
        task = run_job_automation.delay(current_user.id, job_preferences)
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Automation session started'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error starting automation via API: {e}")
        return jsonify({'error': 'Failed to start automation'}), 500

@bp.route('/automation/status/<task_id>', methods=['GET'])
@login_required
def get_automation_status_api(task_id):
    """Get automation task status via API"""
    try:
        task = run_job_automation.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'state': task.state,
                'status': 'Task is waiting to start...'
            }
        elif task.state == 'PROGRESS':
            response = {
                'state': task.state,
                'status': task.info.get('status', ''),
                'session_id': task.info.get('session_id')
            }
        elif task.state == 'SUCCESS':
            response = {
                'state': task.state,
                'status': 'Completed',
                'results': task.info
            }
        else:  # FAILURE
            response = {
                'state': task.state,
                'status': 'Failed',
                'error': str(task.info)
            }
        
        return jsonify(response)
        
    except Exception as e:
        current_app.logger.error(f"Error getting automation status via API: {e}")
        return jsonify({'error': 'Unable to get status'}), 500

@bp.route('/settings', methods=['GET'])
@login_required
def get_user_settings():
    """Get user settings and preferences"""
    try:
        settings_data = {
            'email_notifications': True,
            'daily_summary_email': True,
            'resume_uploaded': False,
            'job_preferences': {},
            'automation_settings': {}
        }
        
        if current_user.user_settings:
            settings = current_user.user_settings
            settings_data.update({
                'email_notifications': settings.email_notifications,
                'daily_summary_email': settings.daily_summary_email,
                'resume_uploaded': bool(settings.resume_file_path),
                'resume_filename': settings.resume_filename,
                'resume_uploaded_at': settings.resume_uploaded_at.isoformat() if settings.resume_uploaded_at else None,
                'job_preferences': settings.get_job_preferences(),
                'automation_settings': settings.get_automation_settings()
            })
        
        return jsonify(settings_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting user settings: {e}")
        return jsonify({'error': 'Unable to get settings'}), 500

@bp.route('/settings', methods=['PUT'])
@login_required
def update_user_settings():
    """Update user settings and preferences"""
    try:
        data = request.get_json()
        
        # Get or create user settings
        if not current_user.user_settings:
            user_settings = UserSettings(user_id=current_user.id)
            db.session.add(user_settings)
        else:
            user_settings = current_user.user_settings
        
        # Update settings
        if 'email_notifications' in data:
            user_settings.email_notifications = bool(data['email_notifications'])
        
        if 'daily_summary_email' in data:
            user_settings.daily_summary_email = bool(data['daily_summary_email'])
        
        if 'job_preferences' in data:
            user_settings.set_job_preferences(data['job_preferences'])
        
        if 'automation_settings' in data:
            user_settings.set_automation_settings(data['automation_settings'])
        
        user_settings.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Settings updated successfully'})
        
    except Exception as e:
        current_app.logger.error(f"Error updating user settings: {e}")
        return jsonify({'error': 'Unable to update settings'}), 500

@bp.route('/health', methods=['GET'])
def api_health_check():
    """API health check endpoint"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'api': 'autoapply_saas'
        })
        
    except Exception as e:
        current_app.logger.error(f"API health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500
