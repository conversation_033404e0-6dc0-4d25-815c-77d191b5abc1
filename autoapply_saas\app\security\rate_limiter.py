"""
Rate limiting and security controls for AutoApply.co.nz
Implements rate limiting, IP blocking, and abuse prevention
"""

import time
from datetime import datetime, timedelta
from flask import request, current_app, g
from functools import wraps
from app import redis_client, db
from app.models import SystemLog
import json

class RateLimiter:
    """Advanced rate limiting with Redis backend"""
    
    def __init__(self):
        self.redis = redis_client
        self.default_limits = {
            'login': {'requests': 5, 'window': 300},  # 5 attempts per 5 minutes
            'register': {'requests': 3, 'window': 3600},  # 3 registrations per hour
            'api': {'requests': 100, 'window': 3600},  # 100 API calls per hour
            'automation': {'requests': 10, 'window': 3600},  # 10 automation starts per hour
            'password_reset': {'requests': 3, 'window': 3600},  # 3 password resets per hour
        }
    
    def is_rate_limited(self, key, limit_type='api', user_id=None):
        """
        Check if a request should be rate limited
        
        Args:
            key: Unique identifier (usually IP address)
            limit_type: Type of rate limit to apply
            user_id: Optional user ID for user-specific limits
            
        Returns:
            tuple: (is_limited: bool, retry_after: int)
        """
        if not self.redis:
            return False, 0
        
        try:
            limits = self.default_limits.get(limit_type, self.default_limits['api'])
            max_requests = limits['requests']
            window = limits['window']
            
            # Create rate limit key
            rate_key = f"rate_limit:{limit_type}:{key}"
            if user_id:
                rate_key += f":{user_id}"
            
            # Get current count
            current_count = self.redis.get(rate_key)
            
            if current_count is None:
                # First request in window
                self.redis.setex(rate_key, window, 1)
                return False, 0
            
            current_count = int(current_count)
            
            if current_count >= max_requests:
                # Rate limited
                ttl = self.redis.ttl(rate_key)
                return True, ttl if ttl > 0 else window
            
            # Increment counter
            self.redis.incr(rate_key)
            return False, 0
            
        except Exception as e:
            current_app.logger.error(f"Rate limiter error: {e}")
            return False, 0
    
    def record_failed_attempt(self, key, attempt_type='login', user_id=None):
        """Record a failed attempt for progressive penalties"""
        if not self.redis:
            return
        
        try:
            failed_key = f"failed_attempts:{attempt_type}:{key}"
            if user_id:
                failed_key += f":{user_id}"
            
            # Increment failed attempts
            count = self.redis.incr(failed_key)
            
            # Set expiry on first failure
            if count == 1:
                self.redis.expire(failed_key, 3600)  # 1 hour expiry
            
            # Progressive penalties
            if count >= 10:
                # Block for 24 hours after 10 failures
                self.block_ip(key, duration=86400, reason=f"Too many failed {attempt_type} attempts")
            elif count >= 5:
                # Temporary block for 1 hour after 5 failures
                self.block_ip(key, duration=3600, reason=f"Multiple failed {attempt_type} attempts")
            
            # Log the failed attempt
            SystemLog.log_event(
                level='WARNING',
                message=f"Failed {attempt_type} attempt from {key}",
                source='security',
                user_id=user_id,
                ip_address=key,
                extra_data={'attempt_count': count, 'attempt_type': attempt_type}
            )
            
        except Exception as e:
            current_app.logger.error(f"Error recording failed attempt: {e}")
    
    def block_ip(self, ip_address, duration=3600, reason="Security violation"):
        """Block an IP address for a specified duration"""
        if not self.redis:
            return
        
        try:
            block_key = f"blocked_ip:{ip_address}"
            block_data = {
                'blocked_at': datetime.utcnow().isoformat(),
                'duration': duration,
                'reason': reason,
                'expires_at': (datetime.utcnow() + timedelta(seconds=duration)).isoformat()
            }
            
            self.redis.setex(block_key, duration, json.dumps(block_data))
            
            # Log the IP block
            SystemLog.log_event(
                level='WARNING',
                message=f"IP address blocked: {ip_address} - {reason}",
                source='security',
                ip_address=ip_address,
                extra_data=block_data
            )
            
            current_app.logger.warning(f"Blocked IP {ip_address} for {duration} seconds: {reason}")
            
        except Exception as e:
            current_app.logger.error(f"Error blocking IP: {e}")
    
    def is_ip_blocked(self, ip_address):
        """Check if an IP address is currently blocked"""
        if not self.redis:
            return False, None
        
        try:
            block_key = f"blocked_ip:{ip_address}"
            block_data = self.redis.get(block_key)
            
            if block_data:
                block_info = json.loads(block_data)
                ttl = self.redis.ttl(block_key)
                return True, {
                    'reason': block_info.get('reason', 'Security violation'),
                    'expires_in': ttl,
                    'blocked_at': block_info.get('blocked_at')
                }
            
            return False, None
            
        except Exception as e:
            current_app.logger.error(f"Error checking IP block: {e}")
            return False, None
    
    def unblock_ip(self, ip_address):
        """Manually unblock an IP address"""
        if not self.redis:
            return False
        
        try:
            block_key = f"blocked_ip:{ip_address}"
            result = self.redis.delete(block_key)
            
            if result:
                SystemLog.log_event(
                    level='INFO',
                    message=f"IP address manually unblocked: {ip_address}",
                    source='security',
                    ip_address=ip_address
                )
                current_app.logger.info(f"Manually unblocked IP {ip_address}")
            
            return bool(result)
            
        except Exception as e:
            current_app.logger.error(f"Error unblocking IP: {e}")
            return False
    
    def get_blocked_ips(self):
        """Get list of currently blocked IP addresses"""
        if not self.redis:
            return []
        
        try:
            blocked_ips = []
            pattern = "blocked_ip:*"
            
            for key in self.redis.scan_iter(match=pattern):
                ip_address = key.decode('utf-8').replace('blocked_ip:', '')
                block_data = self.redis.get(key)
                
                if block_data:
                    block_info = json.loads(block_data)
                    ttl = self.redis.ttl(key)
                    
                    blocked_ips.append({
                        'ip_address': ip_address,
                        'reason': block_info.get('reason', 'Unknown'),
                        'blocked_at': block_info.get('blocked_at'),
                        'expires_in': ttl,
                        'expires_at': block_info.get('expires_at')
                    })
            
            return blocked_ips
            
        except Exception as e:
            current_app.logger.error(f"Error getting blocked IPs: {e}")
            return []

# Global rate limiter instance
rate_limiter = RateLimiter()

def rate_limit(limit_type='api', per_user=False):
    """
    Decorator for rate limiting endpoints
    
    Args:
        limit_type: Type of rate limit to apply
        per_user: Whether to apply limits per user instead of per IP
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get client IP
            client_ip = get_client_ip()
            
            # Check if IP is blocked
            is_blocked, block_info = rate_limiter.is_ip_blocked(client_ip)
            if is_blocked:
                from flask import jsonify
                return jsonify({
                    'error': 'IP address blocked',
                    'reason': block_info['reason'],
                    'expires_in': block_info['expires_in']
                }), 429
            
            # Determine rate limit key
            if per_user and hasattr(g, 'current_user') and g.current_user:
                key = str(g.current_user.id)
                user_id = g.current_user.id
            else:
                key = client_ip
                user_id = getattr(g, 'current_user', {}).get('id') if hasattr(g, 'current_user') else None
            
            # Check rate limit
            is_limited, retry_after = rate_limiter.is_rate_limited(key, limit_type, user_id)
            
            if is_limited:
                from flask import jsonify
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'retry_after': retry_after,
                    'limit_type': limit_type
                }), 429
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def get_client_ip():
    """Get the real client IP address"""
    # Check for forwarded headers (when behind proxy/load balancer)
    if request.headers.get('X-Forwarded-For'):
        # Get the first IP in the chain (original client)
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    elif request.headers.get('CF-Connecting-IP'):  # Cloudflare
        return request.headers.get('CF-Connecting-IP')
    else:
        return request.remote_addr

def log_security_event(event_type, message, user_id=None, severity='INFO', additional_data=None):
    """Log security-related events"""
    try:
        client_ip = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')
        
        extra_data = {
            'event_type': event_type,
            'ip_address': client_ip,
            'user_agent': user_agent,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if additional_data:
            extra_data.update(additional_data)
        
        SystemLog.log_event(
            level=severity,
            message=message,
            source='security',
            user_id=user_id,
            ip_address=client_ip,
            user_agent=user_agent,
            extra_data=extra_data
        )
        
    except Exception as e:
        current_app.logger.error(f"Error logging security event: {e}")

def require_https():
    """Decorator to require HTTPS for sensitive endpoints"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_secure and not current_app.debug:
                from flask import redirect, url_for
                return redirect(url_for(request.endpoint, _external=True, _scheme='https', **request.view_args))
            return f(*args, **kwargs)
        return decorated_function
    return decorator
