{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Blocked IP Addresses</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshBlockedIPs()">
                    <i class="fas fa-sync me-2"></i>Refresh
                </button>
                <button class="btn btn-outline-success" onclick="showUnblockModal()">
                    <i class="fas fa-unlock me-2"></i>Manual Unblock
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Security Overview -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-ban text-danger fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ blocked_ips|length }}</h4>
                <p class="text-muted mb-0">Currently Blocked</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt text-success fa-2x mb-3"></i>
                <h4 class="fw-bold">Active</h4>
                <p class="text-muted mb-0">Protection Status</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-clock text-warning fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ blocked_ips|selectattr('expires_in', 'lessthan', 3600)|list|length }}</h4>
                <p class="text-muted mb-0">Expiring Soon</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle text-info fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ blocked_ips|selectattr('reason', 'containing', 'malicious')|list|length }}</h4>
                <p class="text-muted mb-0">Malicious Activity</p>
            </div>
        </div>
    </div>
</div>

<!-- Blocked IPs Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Blocked IP Addresses
                </h5>
            </div>
            <div class="card-body">
                {% if blocked_ips %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>IP Address</th>
                                    <th>Reason</th>
                                    <th>Blocked At</th>
                                    <th>Expires</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ip in blocked_ips %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-globe text-muted me-2"></i>
                                            <div>
                                                <div class="fw-bold font-monospace">{{ ip.ip_address }}</div>
                                                <small class="text-muted">{{ ip.ip_address | geolocate if ip.ip_address | geolocate else 'Unknown Location' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if 'malicious' in ip.reason.lower() %}
                                            <span class="badge bg-danger">{{ ip.reason }}</span>
                                        {% elif 'failed' in ip.reason.lower() %}
                                            <span class="badge bg-warning">{{ ip.reason }}</span>
                                        {% elif 'suspicious' in ip.reason.lower() %}
                                            <span class="badge bg-info">{{ ip.reason }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ ip.reason }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ip.blocked_at %}
                                            <div>{{ moment(ip.blocked_at).format('MMM DD, YYYY') }}</div>
                                            <small class="text-muted">{{ moment(ip.blocked_at).format('HH:mm:ss') }}</small>
                                        {% else %}
                                            <span class="text-muted">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ip.expires_in > 0 %}
                                            {% if ip.expires_in < 3600 %}
                                                <span class="text-warning fw-bold">{{ (ip.expires_in / 60) | round }} min</span>
                                            {% elif ip.expires_in < 86400 %}
                                                <span class="text-info">{{ (ip.expires_in / 3600) | round }} hours</span>
                                            {% else %}
                                                <span class="text-muted">{{ (ip.expires_in / 86400) | round }} days</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-danger">Expired</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ip.expires_in > 0 %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-ban me-1"></i>Blocked
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-clock me-1"></i>Expired
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="unblockIP('{{ ip.ip_address }}')"
                                                    title="Unblock IP">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" 
                                                    onclick="viewIPDetails('{{ ip.ip_address }}')"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-warning" 
                                                    onclick="extendBlock('{{ ip.ip_address }}')"
                                                    title="Extend Block">
                                                <i class="fas fa-clock"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shield-alt text-success fa-3x mb-4"></i>
                        <h4 class="text-success">No Blocked IP Addresses</h4>
                        <p class="text-muted mb-4">All IP addresses are currently allowed access.</p>
                        <button class="btn btn-outline-primary" onclick="showBlockModal()">
                            <i class="fas fa-ban me-2"></i>Block IP Address
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Manual Unblock Modal -->
<div class="modal fade" id="unblockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-unlock me-2"></i>Unblock IP Address
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="unblockForm">
                    <div class="mb-3">
                        <label class="form-label">IP Address</label>
                        <input type="text" class="form-control font-monospace" id="unblockIP" 
                               placeholder="***********" required>
                        <div class="form-text">Enter the IP address to unblock</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Reason for Unblocking</label>
                        <textarea class="form-control" id="unblockReason" rows="3" 
                                  placeholder="Explain why this IP should be unblocked..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmUnblock()">
                    <i class="fas fa-unlock me-2"></i>Unblock IP
                </button>
            </div>
        </div>
    </div>
</div>

<!-- IP Details Modal -->
<div class="modal fade" id="ipDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>IP Address Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="ipDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function refreshBlockedIPs() {
    window.location.reload();
}

function showUnblockModal() {
    new bootstrap.Modal(document.getElementById('unblockModal')).show();
}

async function unblockIP(ipAddress) {
    if (!confirm(`Are you sure you want to unblock IP address ${ipAddress}?`)) {
        return;
    }
    
    try {
        const response = await fetch('/security/admin/api/unblock-ip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ip_address: ipAddress,
                reason: 'Manual unblock by admin'
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to unblock IP', 'error');
        }
    } catch (error) {
        console.error('Error unblocking IP:', error);
        showAdminToast('Network error', 'error');
    }
}

async function confirmUnblock() {
    const ipAddress = document.getElementById('unblockIP').value;
    const reason = document.getElementById('unblockReason').value;
    
    if (!ipAddress) {
        showAdminToast('Please enter an IP address', 'error');
        return;
    }
    
    try {
        const response = await fetch('/security/admin/api/unblock-ip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ip_address: ipAddress,
                reason: reason || 'Manual unblock by admin'
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('unblockModal')).hide();
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to unblock IP', 'error');
        }
    } catch (error) {
        console.error('Error unblocking IP:', error);
        showAdminToast('Network error', 'error');
    }
}

function viewIPDetails(ipAddress) {
    // Mock IP details - in production, this would fetch real data
    const mockDetails = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="fw-bold">IP Information</h6>
                <p><strong>IP Address:</strong> ${ipAddress}</p>
                <p><strong>Location:</strong> Auckland, New Zealand</p>
                <p><strong>ISP:</strong> Spark New Zealand</p>
                <p><strong>Organization:</strong> Residential</p>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">Block Information</h6>
                <p><strong>Blocked At:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>Reason:</strong> Multiple failed login attempts</p>
                <p><strong>Block Count:</strong> 3 times</p>
                <p><strong>Last Activity:</strong> 2 hours ago</p>
            </div>
        </div>
        <hr>
        <div>
            <h6 class="fw-bold">Recent Activity</h6>
            <div class="bg-light rounded p-3">
                <div class="small">
                    <div class="mb-1"><span class="text-danger">Failed Login</span> - 2 hours ago</div>
                    <div class="mb-1"><span class="text-danger">Failed Login</span> - 2 hours ago</div>
                    <div class="mb-1"><span class="text-warning">Suspicious Request</span> - 3 hours ago</div>
                    <div class="mb-1"><span class="text-info">Normal Request</span> - 4 hours ago</div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('ipDetailsContent').innerHTML = mockDetails;
    new bootstrap.Modal(document.getElementById('ipDetailsModal')).show();
}

function extendBlock(ipAddress) {
    if (confirm(`Extend block duration for IP address ${ipAddress}?`)) {
        showAdminToast('Block extension feature coming soon!', 'info');
    }
}

function showBlockModal() {
    showAdminToast('Manual IP blocking feature coming soon!', 'info');
}
</script>
{% endblock %}
