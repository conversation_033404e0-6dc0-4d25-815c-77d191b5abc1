"""
Dashboard routes for AutoApply.co.nz
User dashboard and application management
"""

from datetime import datetime, timedelta
from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.dashboard import bp
from app.models import User, JobApplication, AutomationSession, UserSettings
from app.automation.tasks import run_job_automation
import os

@bp.route('/')
@login_required
def index():
    """Main dashboard page"""
    # Check if user needs to verify email
    if not current_user.is_verified:
        flash('Please verify your email address to access the dashboard.', 'warning')
        return redirect(url_for('auth.resend_verification'))
    
    # Get dashboard statistics
    stats = get_user_statistics(current_user.id)
    
    # Get recent applications
    recent_applications = current_user.job_applications.order_by(
        JobApplication.applied_at.desc()
    ).limit(5).all()
    
    # Get recent automation sessions
    recent_sessions = current_user.automation_sessions.order_by(
        AutomationSession.started_at.desc()
    ).limit(3).all()
    
    return render_template('dashboard/index.html',
                         title='Dashboard - AutoApply.co.nz',
                         stats=stats,
                         recent_applications=recent_applications,
                         recent_sessions=recent_sessions)

@bp.route('/applications')
@login_required
def applications():
    """Applications history page"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('JOBS_PER_PAGE', 20)
    
    applications = current_user.job_applications.order_by(
        JobApplication.applied_at.desc()
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('dashboard/applications.html',
                         title='Applications - AutoApply.co.nz',
                         applications=applications)

@bp.route('/automation')
@login_required
def automation():
    """Automation control page"""
    # Check if user can run automation
    if not current_user.can_apply_to_jobs():
        flash('You need an active subscription or trial to use automation.', 'warning')
        return redirect(url_for('payments.subscribe'))
    
    # Get automation sessions
    sessions = current_user.automation_sessions.order_by(
        AutomationSession.started_at.desc()
    ).limit(10).all()
    
    # Check for running sessions
    running_session = current_user.automation_sessions.filter_by(status='running').first()
    
    return render_template('dashboard/automation.html',
                         title='Automation - AutoApply.co.nz',
                         sessions=sessions,
                         running_session=running_session)

@bp.route('/start-automation', methods=['POST'])
@login_required
def start_automation():
    """Start automation session"""
    try:
        # Check if user can run automation
        if not current_user.can_apply_to_jobs():
            return jsonify({'error': 'Subscription or trial required'}), 403
        
        # Check for existing running session
        running_session = current_user.automation_sessions.filter_by(status='running').first()
        if running_session:
            return jsonify({'error': 'Automation session already running'}), 400
        
        # Check daily application limit
        applications_today = current_user.get_applications_today()
        max_daily = current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50)
        
        if applications_today >= max_daily:
            return jsonify({'error': f'Daily application limit of {max_daily} reached'}), 400
        
        # Get job preferences from request
        job_preferences = request.json.get('preferences', {})
        
        # Start automation task
        task = run_job_automation.delay(current_user.id, job_preferences)
        
        return jsonify({
            'success': True,
            'task_id': task.id,
            'message': 'Automation session started'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error starting automation for user {current_user.id}: {e}")
        return jsonify({'error': 'Failed to start automation'}), 500

@bp.route('/automation-status/<task_id>')
@login_required
def automation_status(task_id):
    """Get automation task status"""
    try:
        task = run_job_automation.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'state': task.state,
                'status': 'Task is waiting to start...'
            }
        elif task.state == 'PROGRESS':
            response = {
                'state': task.state,
                'status': task.info.get('status', ''),
                'session_id': task.info.get('session_id')
            }
        elif task.state == 'SUCCESS':
            response = {
                'state': task.state,
                'status': 'Completed',
                'results': task.info
            }
        else:  # FAILURE
            response = {
                'state': task.state,
                'status': 'Failed',
                'error': str(task.info)
            }
        
        return jsonify(response)
        
    except Exception as e:
        current_app.logger.error(f"Error getting automation status: {e}")
        return jsonify({'error': 'Unable to get status'}), 500

@bp.route('/settings')
@login_required
def settings():
    """User settings page"""
    return render_template('dashboard/settings.html',
                         title='Settings - AutoApply.co.nz')

@bp.route('/upload-resume', methods=['POST'])
@login_required
def upload_resume():
    """Upload user resume"""
    try:
        if 'resume' not in request.files:
            return jsonify({'error': 'No file selected'}), 400
        
        file = request.files['resume']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Check file extension
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'pdf', 'doc', 'docx'})
        if not file.filename.lower().endswith(tuple(allowed_extensions)):
            return jsonify({'error': 'Invalid file type. Please upload PDF, DOC, or DOCX files.'}), 400
        
        # Secure filename
        filename = secure_filename(file.filename)
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        filename = f"resume_{current_user.id}_{timestamp}_{filename}"
        
        # Create upload directory
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        user_folder = os.path.join(upload_folder, 'resumes', str(current_user.id))
        os.makedirs(user_folder, exist_ok=True)
        
        # Save file
        file_path = os.path.join(user_folder, filename)
        file.save(file_path)
        
        # Update user settings
        if not current_user.user_settings:
            user_settings = UserSettings(user_id=current_user.id)
            db.session.add(user_settings)
        else:
            user_settings = current_user.user_settings
        
        user_settings.resume_filename = filename
        user_settings.resume_file_path = file_path
        user_settings.resume_uploaded_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Resume uploaded successfully',
            'filename': filename
        })
        
    except Exception as e:
        current_app.logger.error(f"Error uploading resume for user {current_user.id}: {e}")
        return jsonify({'error': 'Failed to upload resume'}), 500

@bp.route('/billing')
@login_required
def billing():
    """Billing and subscription management page"""
    return render_template('dashboard/billing.html',
                         title='Billing - AutoApply.co.nz')

def get_user_statistics(user_id):
    """Get comprehensive user statistics"""
    try:
        user = User.query.get(user_id)
        
        # Basic counts
        total_applications = user.job_applications.count()
        total_sessions = user.automation_sessions.count()
        
        # Today's applications
        today = datetime.utcnow().date()
        applications_today = user.job_applications.filter(
            db.func.date(JobApplication.applied_at) == today
        ).count()
        
        # This month's applications
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        applications_this_month = user.job_applications.filter(
            JobApplication.applied_at >= month_start
        ).count()
        
        # Success rate (completed sessions vs total sessions)
        completed_sessions = user.automation_sessions.filter_by(status='completed').count()
        success_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
        
        # Average applications per session
        avg_applications = (total_applications / completed_sessions) if completed_sessions > 0 else 0
        
        # Recent activity (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_applications = user.job_applications.filter(
            JobApplication.applied_at >= week_ago
        ).count()
        
        # Trial/subscription info
        trial_info = {}
        if user.trial_started_at:
            trial_info = {
                'applications_used': user.trial_applications_used,
                'applications_remaining': current_app.config.get('FREE_TRIAL_APPLICATIONS', 10) - user.trial_applications_used,
                'is_active': user.is_trial_active()
            }
        
        subscription_info = {}
        if user.subscription:
            subscription_info = {
                'status': user.subscription.status,
                'is_active': user.subscription.is_active(),
                'days_remaining': user.subscription.days_remaining(),
                'cancel_at_period_end': user.subscription.cancel_at_period_end
            }
        
        return {
            'total_applications': total_applications,
            'applications_today': applications_today,
            'applications_this_month': applications_this_month,
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'success_rate': round(success_rate, 1),
            'avg_applications_per_session': round(avg_applications, 1),
            'recent_applications': recent_applications,
            'trial_info': trial_info,
            'subscription_info': subscription_info,
            'can_apply_jobs': user.can_apply_to_jobs(),
            'max_daily_applications': current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50),
            'applications_remaining_today': max(0, current_app.config.get('MAX_APPLICATIONS_PER_DAY', 50) - applications_today)
        }
        
    except Exception as e:
        current_app.logger.error(f"Error getting user statistics: {e}")
        return {}
