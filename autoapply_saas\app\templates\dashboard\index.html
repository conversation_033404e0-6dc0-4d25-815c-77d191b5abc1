{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Dashboard</h1>
            <div class="d-flex gap-2">
                {% if current_user.can_apply_to_jobs() %}
                    <button id="startAutomationBtn" class="btn btn-primary">
                        <i class="fas fa-play me-2"></i>Start Automation
                    </button>
                {% else %}
                    <a href="{{ url_for('payments.subscribe') }}" class="btn btn-outline-primary">
                        <i class="fas fa-crown me-2"></i>Upgrade to Apply
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Status Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-paper-plane text-primary fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Applications Today</div>
                        <div class="h4 mb-0" id="applicationsToday">{{ stats.applications_today or 0 }}</div>
                        <small class="text-muted">of {{ stats.max_daily_applications }} limit</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-chart-line text-success fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">This Month</div>
                        <div class="h4 mb-0" id="applicationsThisMonth">{{ stats.applications_this_month or 0 }}</div>
                        <small class="text-muted">applications sent</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-percentage text-info fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Success Rate</div>
                        <div class="h4 mb-0" id="successRate">{{ stats.success_rate or 0 }}%</div>
                        <small class="text-muted">automation success</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-warning fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Avg per Session</div>
                        <div class="h4 mb-0" id="avgApplications">{{ stats.avg_applications_per_session or 0 }}</div>
                        <small class="text-muted">applications</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Status -->
{% if current_user.trial_started_at or current_user.subscription %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                {% if current_user.is_trial_active() %}
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title mb-1">
                                <i class="fas fa-gift text-primary me-2"></i>Free Trial Active
                            </h5>
                            <p class="text-muted mb-0">
                                {{ stats.trial_info.applications_remaining }} applications remaining
                            </p>
                        </div>
                        <div class="text-end">
                            <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary">
                                Upgrade Now
                            </a>
                        </div>
                    </div>
                {% elif current_user.has_active_subscription() %}
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h5 class="card-title mb-1">
                                <i class="fas fa-crown text-success me-2"></i>Premium Subscription
                            </h5>
                            <p class="text-muted mb-0">
                                {% if stats.subscription_info.cancel_at_period_end %}
                                    Cancels in {{ stats.subscription_info.days_remaining }} days
                                {% else %}
                                    Renews in {{ stats.subscription_info.days_remaining }} days
                                {% endif %}
                            </p>
                        </div>
                        <div class="text-end">
                            <a href="{{ url_for('dashboard.billing') }}" class="btn btn-outline-primary">
                                Manage Billing
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Automation Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>Automation Status
                </h5>
            </div>
            <div class="card-body">
                <div id="automationStatus">
                    {% if running_session %}
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div>
                                    <div class="fw-bold">Automation Running</div>
                                    <small class="text-muted">Started {{ running_session.started_at.strftime('%H:%M') }}</small>
                                </div>
                            </div>
                            <button id="stopAutomationBtn" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-stop me-2"></i>Stop
                            </button>
                        </div>
                        <div class="mt-3">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <small class="text-muted">Processing jobs...</small>
                                <small class="text-muted" id="sessionDuration">{{ running_session.duration_minutes() }} min</small>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-pause-circle text-muted fa-2x mb-3"></i>
                            <div class="fw-bold text-muted">No Active Automation</div>
                            <small class="text-muted">Click "Start Automation" to begin applying to jobs</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Applications
                    </h5>
                    <a href="{{ url_for('dashboard.applications') }}" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_applications %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Job Title</th>
                                    <th>Company</th>
                                    <th>Status</th>
                                    <th>Applied</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for app in recent_applications %}
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ app.job_title }}</div>
                                        {% if app.cover_letter_generated %}
                                            <small class="text-success">
                                                <i class="fas fa-robot me-1"></i>AI Cover Letter
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>{{ app.company_name }}</td>
                                    <td>
                                        {% if app.application_status == 'submitted' %}
                                            <span class="badge bg-success">Submitted</span>
                                        {% elif app.application_status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ app.application_status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ app.applied_at.strftime('%b %d, %H:%M') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox text-muted fa-2x mb-3"></i>
                        <div class="fw-bold text-muted">No Applications Yet</div>
                        <small class="text-muted">Start automation to see your applications here</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('dashboard.automation') }}" class="btn btn-outline-primary">
                        <i class="fas fa-robot me-2"></i>Automation Settings
                    </a>
                    <a href="{{ url_for('dashboard.settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-upload me-2"></i>Upload Resume
                    </a>
                    <a href="{{ url_for('dashboard.applications') }}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>View All Applications
                    </a>
                    <a href="{{ url_for('dashboard.billing') }}" class="btn btn-outline-warning">
                        <i class="fas fa-credit-card me-2"></i>Billing & Subscription
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Recent Sessions -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Sessions
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                    {% for session in recent_sessions %}
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <div class="fw-bold small">
                                {% if session.status == 'completed' %}
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                {% elif session.status == 'failed' %}
                                    <i class="fas fa-times-circle text-danger me-1"></i>
                                {% elif session.status == 'running' %}
                                    <i class="fas fa-spinner fa-spin text-primary me-1"></i>
                                {% else %}
                                    <i class="fas fa-pause-circle text-muted me-1"></i>
                                {% endif %}
                                {{ session.status.title() }}
                            </div>
                            <small class="text-muted">
                                {{ session.applications_submitted }} applications
                            </small>
                        </div>
                        <small class="text-muted">
                            {{ session.started_at.strftime('%b %d') }}
                        </small>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-history text-muted fa-lg mb-2"></i>
                        <div class="small text-muted">No sessions yet</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Automation Modal -->
<div class="modal fade" id="automationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-robot me-2"></i>Start Job Automation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    The automation will search for jobs on Seek.co.nz and apply to those with Quick Apply buttons.
                </div>
                
                <form id="automationForm">
                    <div class="mb-3">
                        <label class="form-label">Job Search Keywords (Optional)</label>
                        <input type="text" class="form-control" name="keywords" 
                               placeholder="e.g., software developer, marketing">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Location (Optional)</label>
                        <input type="text" class="form-control" name="location" 
                               placeholder="e.g., Auckland, Wellington">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="generate_cover_letters" 
                                   id="generateCoverLetters" checked>
                            <label class="form-check-label" for="generateCoverLetters">
                                Generate AI cover letters
                            </label>
                        </div>
                    </div>
                </form>
                
                <div class="bg-light rounded p-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold">{{ stats.applications_today or 0 }}</div>
                            <small class="text-muted">Today</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">{{ stats.applications_remaining_today or 0 }}</div>
                            <small class="text-muted">Remaining</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold">{{ stats.max_daily_applications }}</div>
                            <small class="text-muted">Daily Limit</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStartAutomation">
                    <i class="fas fa-play me-2"></i>Start Automation
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
{% endblock %}
