{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">User Management</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <button class="btn btn-outline-primary" onclick="exportUsers()">
                    <i class="fas fa-download me-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Search users..." value="{{ search }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ 'selected' if status == 'active' else '' }}>Active</option>
                            <option value="inactive" {{ 'selected' if status == 'inactive' else '' }}>Inactive</option>
                            <option value="verified" {{ 'selected' if status == 'verified' else '' }}>Verified</option>
                            <option value="unverified" {{ 'selected' if status == 'unverified' else '' }}>Unverified</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="subscription">
                            <option value="">All Plans</option>
                            <option value="trial" {{ 'selected' if subscription_status == 'trial' else '' }}>Trial</option>
                            <option value="premium" {{ 'selected' if subscription_status == 'premium' else '' }}>Premium</option>
                            <option value="expired" {{ 'selected' if subscription_status == 'expired' else '' }}>Expired</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Users ({{ users.total }} total)
                </h5>
            </div>
            <div class="card-body">
                {% if users.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>User</th>
                                    <th>Status</th>
                                    <th>Plan</th>
                                    <th>Applications</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                {{ user.first_name[0].upper() }}{{ user.last_name[0].upper() }}
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ user.first_name }} {{ user.last_name }}</div>
                                                <small class="text-muted">{{ user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            {% if user.is_active %}
                                                <span class="badge bg-success mb-1">Active</span>
                                            {% else %}
                                                <span class="badge bg-danger mb-1">Inactive</span>
                                            {% endif %}
                                            
                                            {% if user.is_verified %}
                                                <span class="badge bg-info">Verified</span>
                                            {% else %}
                                                <span class="badge bg-warning">Unverified</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if user.has_active_subscription() %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-crown me-1"></i>Premium
                                            </span>
                                        {% elif user.is_trial_active() %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-gift me-1"></i>Trial
                                            </span>
                                            <div class="small text-muted">
                                                {{ user.trial_applications_used }}/10 used
                                            </div>
                                        {% else %}
                                            <span class="badge bg-secondary">Free</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ user.job_applications.count() }}</div>
                                        <small class="text-muted">{{ user.get_applications_today() }} today</small>
                                    </td>
                                    <td>
                                        <div>{{ user.created_at.strftime('%b %d, %Y') }}</div>
                                        <small class="text-muted">{{ user.created_at.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('admin.user_detail', user_id=user.id) }}">
                                                        <i class="fas fa-eye me-2"></i>View Details
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})">
                                                        {% if user.is_active %}
                                                            <i class="fas fa-ban me-2"></i>Deactivate
                                                        {% else %}
                                                            <i class="fas fa-check me-2"></i>Activate
                                                        {% endif %}
                                                    </a>
                                                </li>
                                                {% if user.trial_started_at %}
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="resetUserTrial({{ user.id }})">
                                                        <i class="fas fa-redo me-2"></i>Reset Trial
                                                    </a>
                                                </li>
                                                {% endif %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteUser({{ user.id }})">
                                                        <i class="fas fa-trash me-2"></i>Delete User
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if users.pages > 1 %}
                    <nav aria-label="Users pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, search=search, status=status, subscription=subscription_status) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('admin.users', page=page_num, search=search, status=status, subscription=subscription_status) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, search=search, status=status, subscription=subscription_status) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users text-muted fa-3x mb-4"></i>
                        <h4 class="text-muted">No Users Found</h4>
                        <p class="text-muted mb-4">No users match your current filters.</p>
                        <a href="{{ url_for('admin.users') }}" class="btn btn-primary">
                            <i class="fas fa-refresh me-2"></i>Clear Filters
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
async function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    
    if (!confirm(`Are you sure you want to ${action} this user?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to update user status', 'error');
        }
    } catch (error) {
        console.error('Error toggling user status:', error);
        showAdminToast('Network error', 'error');
    }
}

async function resetUserTrial(userId) {
    if (!confirm('Are you sure you want to reset this user\'s trial? This will give them a fresh 7-day trial with 10 applications.')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/users/${userId}/reset-trial`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to reset trial', 'error');
        }
    } catch (error) {
        console.error('Error resetting trial:', error);
        showAdminToast('Network error', 'error');
    }
}

function deleteUser(userId) {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone and will remove all their data.')) {
        return;
    }
    
    // Implementation for user deletion
    showAdminToast('User deletion feature coming soon!', 'info');
}

function exportUsers() {
    // Implementation for user export
    showAdminToast('User export feature coming soon!', 'info');
}
</script>
{% endblock %}
