"""
Job processing service for AutoApply.co.nz
Handles job searching, filtering, and application automation
Refactored from original seek_auto_apply.py
"""

import time
import random
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from flask import current_app
from app import db
from app.models import JobApplication, User
from app.automation.cover_letter_generator import CoverLetterGenerator

class JobProcessor:
    """Handles job processing and application automation"""
    
    def __init__(self, driver_wrapper, user_id, session_id):
        self.driver_wrapper = driver_wrapper
        self.driver = driver_wrapper.driver
        self.user_id = user_id
        self.session_id = session_id
        self.cover_letter_generator = CoverLetterGenerator()
        self.rate_limited = False
        
    def process_jobs(self, job_preferences=None):
        """
        Main job processing method
        
        Args:
            job_preferences: Optional job preferences override
            
        Returns:
            dict: Processing results
        """
        results = {
            'jobs_found': 0,
            'applications_submitted': 0,
            'applications_failed': 0,
            'rate_limited': False,
            'errors': []
        }
        
        try:
            current_app.logger.info(f"Starting job processing for user {self.user_id}")
            
            # Navigate to job search page
            if not self._navigate_to_job_search():
                results['errors'].append('Failed to navigate to job search page')
                return results
            
            # Find job listings
            job_listings = self._find_job_listings()
            results['jobs_found'] = len(job_listings)
            
            current_app.logger.info(f"Found {len(job_listings)} job listings")
            
            # Process each job
            for i, job_element in enumerate(job_listings):
                try:
                    # Check for rate limiting before processing each job
                    if self.rate_limited:
                        current_app.logger.info("Rate limiting detected, stopping job processing")
                        results['rate_limited'] = True
                        break
                    
                    current_app.logger.info(f"Processing job {i+1}/{len(job_listings)}")
                    
                    # Extract job information
                    job_info = self._extract_job_info(job_element)
                    if not job_info:
                        current_app.logger.warning(f"Could not extract job info for job {i+1}")
                        continue
                    
                    # Check if job has Quick Apply
                    if not self._has_quick_apply(job_element):
                        current_app.logger.info(f"Job {i+1} does not have Quick Apply, skipping")
                        continue
                    
                    # Apply to job
                    success = self._apply_to_job(job_info, job_element)
                    
                    if success:
                        results['applications_submitted'] += 1
                        current_app.logger.info(f"Successfully applied to job {i+1}")
                    else:
                        results['applications_failed'] += 1
                        current_app.logger.warning(f"Failed to apply to job {i+1}")
                    
                    # Add delay between applications
                    time.sleep(random.uniform(2.0, 5.0))
                    
                except Exception as e:
                    current_app.logger.error(f"Error processing job {i+1}: {e}")
                    results['applications_failed'] += 1
                    results['errors'].append(f"Job {i+1}: {str(e)}")
                    continue
            
            current_app.logger.info(f"Job processing completed: {results}")
            return results
            
        except Exception as e:
            current_app.logger.error(f"Error in job processing: {e}")
            results['errors'].append(str(e))
            return results
    
    def _navigate_to_job_search(self):
        """Navigate to job search page"""
        try:
            # This would be customized based on the specific job search URL
            # For now, assume we're already on a job search page or navigate to one
            search_url = "https://www.seek.co.nz/jobs"
            self.driver.get(search_url)
            
            # Wait for page to load
            time.sleep(random.uniform(3.0, 5.0))
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error navigating to job search: {e}")
            return False
    
    def _find_job_listings(self):
        """Find job listing elements on the page"""
        try:
            # Updated selectors based on the original code
            job_elements = self.driver.find_elements(By.CSS_SELECTOR, "div._32fem00[data-search-sol-meta]")
            
            # Fallback to old format if new format returns no results
            if len(job_elements) == 0:
                current_app.logger.info("No jobs found with new format, trying old format...")
                job_elements = self.driver.find_elements(By.CSS_SELECTOR, "div.xhgj00[data-search-sol-meta]")
            
            current_app.logger.info(f"Found {len(job_elements)} job elements")
            return job_elements
            
        except Exception as e:
            current_app.logger.error(f"Error finding job listings: {e}")
            return []
    
    def _extract_job_info(self, job_element):
        """Extract job information from job element"""
        try:
            job_info = {}
            
            # Extract job title
            try:
                title_element = job_element.find_element(By.CSS_SELECTOR, "a[data-automation='jobTitle']")
                job_info['title'] = title_element.text.strip()
                job_info['url'] = title_element.get_attribute('href')
            except NoSuchElementException:
                current_app.logger.warning("Could not find job title")
                return None
            
            # Extract company name
            try:
                company_element = job_element.find_element(By.CSS_SELECTOR, "a[data-automation='jobCompany']")
                job_info['company'] = company_element.text.strip()
            except NoSuchElementException:
                job_info['company'] = "Unknown Company"
            
            # Extract location
            try:
                location_element = job_element.find_element(By.CSS_SELECTOR, "a[data-automation='jobLocation']")
                job_info['location'] = location_element.text.strip()
            except NoSuchElementException:
                job_info['location'] = "Unknown Location"
            
            # Extract salary if available
            try:
                salary_element = job_element.find_element(By.CSS_SELECTOR, "span[data-automation='jobSalary']")
                job_info['salary'] = salary_element.text.strip()
            except NoSuchElementException:
                job_info['salary'] = None
            
            return job_info
            
        except Exception as e:
            current_app.logger.error(f"Error extracting job info: {e}")
            return None
    
    def _has_quick_apply(self, job_element):
        """Check if job has Quick Apply button"""
        try:
            # Look for Quick Apply button using the updated selector from memories
            quick_apply_selectors = [
                "span.ube6hn0.wc8kxl63.wc8kxlp.wc8kxl5f.wc8kxl5b.wc8kxlgj.wc8kxlgv.wc8kxly.wc8kxl5.wc8kxlib.wc8kxl4.de1uac2.de1uac7.m81yar11.m81yar13.wc8kxl17.wc8kxl18",
                "button[data-automation='job-apply']",
                "a[data-automation='job-apply']",
                "*[data-automation*='quick-apply']",
                "*[data-automation*='apply']"
            ]
            
            for selector in quick_apply_selectors:
                try:
                    quick_apply_element = job_element.find_element(By.CSS_SELECTOR, selector)
                    if quick_apply_element and quick_apply_element.is_displayed():
                        return True
                except NoSuchElementException:
                    continue
            
            return False
            
        except Exception as e:
            current_app.logger.error(f"Error checking for Quick Apply: {e}")
            return False
    
    def _apply_to_job(self, job_info, job_element):
        """Apply to a specific job"""
        try:
            current_app.logger.info(f"Applying to job: {job_info['title']} at {job_info['company']}")
            
            # Click on the job to open details
            title_link = job_element.find_element(By.CSS_SELECTOR, "a[data-automation='jobTitle']")
            title_link.click()
            
            # Wait for job details to load
            time.sleep(random.uniform(2.0, 4.0))
            
            # Get job description for cover letter generation
            job_description = self._get_job_description()
            
            # Find and click Quick Apply button
            if not self._click_quick_apply():
                return False
            
            # Fill application form
            if not self._fill_application_form(job_info, job_description):
                return False
            
            # Submit application
            if not self._submit_application():
                return False
            
            # Check for rate limiting
            if self._detect_rate_limiter():
                self.rate_limited = True
                return False
            
            # Save application to database
            self._save_application_to_db(job_info, job_description, 'submitted')
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error applying to job: {e}")
            # Save failed application to database
            self._save_application_to_db(job_info, '', 'failed')
            return False

    def _get_job_description(self):
        """Get job description from the current page"""
        try:
            # Look for job description using the updated selector from memories
            description_selectors = [
                "div.ube6hn0.hyknj30[data-automation='jobAdDetails']",
                "div[data-automation='jobAdDetails']",
                "div.job-description",
                ".job-detail-description"
            ]

            for selector in description_selectors:
                try:
                    description_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if description_element:
                        return description_element.get_attribute('innerHTML')
                except NoSuchElementException:
                    continue

            current_app.logger.warning("Could not find job description")
            return ""

        except Exception as e:
            current_app.logger.error(f"Error getting job description: {e}")
            return ""

    def _click_quick_apply(self):
        """Click the Quick Apply button"""
        try:
            # Updated Quick Apply selectors from memories
            quick_apply_selectors = [
                "span.ube6hn0.wc8kxl63.wc8kxlp.wc8kxl5f.wc8kxl5b.wc8kxlgj.wc8kxlgv.wc8kxly.wc8kxl5.wc8kxlib.wc8kxl4.de1uac2.de1uac7.m81yar11.m81yar13.wc8kxl17.wc8kxl18",
                "button[data-automation='job-apply']",
                "a[data-automation='job-apply']",
                "*[data-automation*='quick-apply']",
                "//button[contains(text(), 'Quick Apply')]",
                "//span[contains(text(), 'Quick Apply')]"
            ]

            for selector in quick_apply_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath selector
                        quick_apply_element = self.driver.find_element(By.XPATH, selector)
                    else:
                        # CSS selector
                        quick_apply_element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if quick_apply_element and quick_apply_element.is_displayed():
                        quick_apply_element.click()
                        time.sleep(random.uniform(2.0, 4.0))
                        return True

                except NoSuchElementException:
                    continue

            current_app.logger.warning("Could not find Quick Apply button")
            return False

        except Exception as e:
            current_app.logger.error(f"Error clicking Quick Apply: {e}")
            return False

    def _fill_application_form(self, job_info, job_description):
        """Fill the application form"""
        try:
            current_app.logger.info("Filling application form")

            # Generate cover letter
            cover_letter = self.cover_letter_generator.generate_cover_letter(
                user_id=self.user_id,
                job_description=job_description,
                job_title=job_info.get('title'),
                company_name=job_info.get('company')
            )

            # Fill cover letter field
            self._fill_cover_letter_field(cover_letter)

            # Fill other form fields (select dropdowns, radio buttons, checkboxes, textareas)
            self._fill_select_fields()
            self._fill_radio_buttons()
            self._fill_checkboxes()
            self._fill_textareas()

            return True

        except Exception as e:
            current_app.logger.error(f"Error filling application form: {e}")
            return False

    def _fill_cover_letter_field(self, cover_letter):
        """Fill cover letter textarea"""
        try:
            # Look for cover letter textarea
            cover_letter_selectors = [
                "textarea[data-automation='coverLetter']",
                "textarea[name*='cover']",
                "textarea[placeholder*='cover']",
                "textarea[id*='cover']"
            ]

            for selector in cover_letter_selectors:
                try:
                    textarea = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if textarea and textarea.is_displayed():
                        textarea.clear()
                        textarea.send_keys(cover_letter)
                        current_app.logger.info("Cover letter filled successfully")
                        return True
                except NoSuchElementException:
                    continue

            current_app.logger.info("No cover letter field found")
            return True  # Not an error if no cover letter field

        except Exception as e:
            current_app.logger.error(f"Error filling cover letter: {e}")
            return False

    def _fill_select_fields(self):
        """Fill select dropdown fields automatically"""
        try:
            select_fields = self.driver.find_elements(By.TAG_NAME, "select")

            for select_field in select_fields:
                try:
                    select_obj = Select(select_field)
                    options = select_obj.options
                    option_texts = [opt.text.strip().lower() for opt in options if opt.text.strip()]

                    if len(option_texts) <= 1:
                        continue

                    # Priority selection logic from original code
                    selected = False

                    # Look for NZ citizen option for work authorization
                    for i, option_text in enumerate(option_texts):
                        if "new zealand" in option_text and "citizen" in option_text:
                            select_obj.select_by_index(i)
                            selected = True
                            break

                    # If not selected, choose middle option
                    if not selected and len(option_texts) > 2:
                        middle_index = len(option_texts) // 2
                        select_obj.select_by_index(middle_index)

                except Exception as e:
                    current_app.logger.warning(f"Error filling select field: {e}")
                    continue

            return True

        except Exception as e:
            current_app.logger.error(f"Error filling select fields: {e}")
            return False

    def _fill_radio_buttons(self):
        """Fill radio button fields (always select 'Yes' options)"""
        try:
            radio_buttons = self.driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")

            # Group radio buttons by name
            radio_groups = {}
            for radio in radio_buttons:
                name = radio.get_attribute('name')
                if name:
                    if name not in radio_groups:
                        radio_groups[name] = []
                    radio_groups[name].append(radio)

            # Select "Yes" option for each group
            for group_name, radios in radio_groups.items():
                try:
                    for radio in radios:
                        value = radio.get_attribute('value', '').lower()
                        if 'yes' in value or 'true' in value or value == '1':
                            if radio.is_displayed() and radio.is_enabled():
                                radio.click()
                                break
                except Exception as e:
                    current_app.logger.warning(f"Error selecting radio button in group {group_name}: {e}")
                    continue

            return True

        except Exception as e:
            current_app.logger.error(f"Error filling radio buttons: {e}")
            return False

    def _fill_checkboxes(self):
        """Fill checkbox fields (select all except 'None' options)"""
        try:
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")

            for checkbox in checkboxes:
                try:
                    # Get associated label text
                    label_text = ""
                    checkbox_id = checkbox.get_attribute('id')
                    if checkbox_id:
                        try:
                            label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                            label_text = label.text.strip().lower()
                        except NoSuchElementException:
                            pass

                    # Skip "None" options
                    if 'none' in label_text or 'no' in label_text:
                        continue

                    # Select checkbox if not already selected
                    if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                        checkbox.click()

                except Exception as e:
                    current_app.logger.warning(f"Error selecting checkbox: {e}")
                    continue

            return True

        except Exception as e:
            current_app.logger.error(f"Error filling checkboxes: {e}")
            return False

    def _fill_textareas(self):
        """Fill textarea fields with AI-generated content"""
        try:
            textareas = self.driver.find_elements(By.TAG_NAME, "textarea")

            for textarea in textareas:
                try:
                    # Skip if already filled (like cover letter)
                    if textarea.get_attribute('value'):
                        continue

                    # Get associated label text
                    label_text = ""
                    textarea_id = textarea.get_attribute('id')
                    if textarea_id:
                        try:
                            label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{textarea_id}']")
                            label_text = label.text.strip()
                        except NoSuchElementException:
                            pass

                    if not label_text:
                        # Try to find label by proximity or placeholder
                        placeholder = textarea.get_attribute('placeholder')
                        if placeholder:
                            label_text = placeholder

                    if label_text:
                        # Generate answer using AI
                        answer = self.cover_letter_generator.generate_textarea_answer(
                            user_id=self.user_id,
                            question_text=label_text
                        )

                        textarea.clear()
                        textarea.send_keys(answer)

                except Exception as e:
                    current_app.logger.warning(f"Error filling textarea: {e}")
                    continue

            return True

        except Exception as e:
            current_app.logger.error(f"Error filling textareas: {e}")
            return False

    def _submit_application(self):
        """Submit the application"""
        try:
            # Click Continue buttons first
            continue_selectors = [
                "button[data-testid='continue-button']",
                "//button[contains(text(), 'Continue')]",
                "//button[.//span[contains(text(), 'Continue')]]"
            ]

            for selector in continue_selectors:
                try:
                    if selector.startswith("//"):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button and button.is_displayed():
                        button.click()
                        time.sleep(random.uniform(2.0, 3.0))

                except NoSuchElementException:
                    continue

            # Click final Submit Application button
            submit_selectors = [
                "button[data-testid='review-submit-application']",
                "//span[contains(text(), 'Submit application')]",
                "//button[contains(text(), 'Submit application')]",
                "//*[contains(text(), 'Submit application') and (self::button or self::span or self::div)]"
            ]

            for selector in submit_selectors:
                try:
                    if selector.startswith("//"):
                        submit_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if submit_button and submit_button.is_displayed():
                        submit_button.click()
                        time.sleep(random.uniform(2.0, 4.0))
                        return True

                except NoSuchElementException:
                    continue

            current_app.logger.warning("Could not find Submit Application button")
            return False

        except Exception as e:
            current_app.logger.error(f"Error submitting application: {e}")
            return False

    def _detect_rate_limiter(self):
        """Detect if rate limiter error appears"""
        try:
            # Rate limiter selectors from memories
            rate_limiter_selectors = [
                "div._1bdiith0._1m460an7z._1m460an93._1m460anav._1m460an9r._1m460an67._1m460an5f._1m460ani7._6bcfdt18._6bcfdt1b._6bcfdt1c._6bcfdt1l._1m460an1t._1m460an2i#errorPanel",
                "div[id='errorPanel']",
                "div[role='alert'][aria-live='polite']",
                "//span[contains(text(), 'Something has gone wrong')]",
                "//*[contains(text(), 'Something has gone wrong')]"
            ]

            for selector in rate_limiter_selectors:
                try:
                    if selector.startswith("//"):
                        error_element = self.driver.find_element(By.XPATH, selector)
                    else:
                        error_element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if error_element and error_element.is_displayed():
                        error_message = error_element.text.strip().lower()

                        # Check for rate limit indicators
                        rate_limit_indicators = [
                            "something has gone wrong",
                            "try again",
                            "contact customer service",
                            "cooldown",
                            "too many",
                            "limit exceeded"
                        ]

                        if any(indicator in error_message for indicator in rate_limit_indicators):
                            current_app.logger.warning(f"Rate limiter detected: {error_message}")
                            return True

                except NoSuchElementException:
                    continue

            return False

        except Exception as e:
            current_app.logger.error(f"Error detecting rate limiter: {e}")
            return False

    def _save_application_to_db(self, job_info, job_description, status):
        """Save application to database"""
        try:
            application = JobApplication(
                user_id=self.user_id,
                automation_session_id=self.session_id,
                job_title=job_info.get('title', 'Unknown'),
                company_name=job_info.get('company', 'Unknown'),
                job_url=job_info.get('url', ''),
                job_description=job_description,
                application_status=status,
                cover_letter_generated=bool(job_description),
                applied_at=datetime.utcnow()
            )

            # Set automation metadata
            automation_data = {
                'location': job_info.get('location'),
                'salary': job_info.get('salary'),
                'processed_at': datetime.utcnow().isoformat()
            }
            application.set_automation_data(automation_data)

            db.session.add(application)
            db.session.commit()

            current_app.logger.info(f"Saved application to database: {job_info.get('title')}")

        except Exception as e:
            current_app.logger.error(f"Error saving application to database: {e}")
            db.session.rollback()
