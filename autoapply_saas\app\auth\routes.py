"""
Authentication routes for AutoApply.co.nz
"""

from datetime import datetime
from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_user, logout_user, current_user, login_required
from app import db
from app.auth import bp
from app.auth.forms import (
    LoginForm, RegistrationForm, RequestPasswordResetForm, 
    ResetPasswordForm, ChangePasswordForm, EmailVerificationForm
)
from app.models import User, UserSettings
from app.auth.email import send_verification_email, send_password_reset_email
import jwt

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login route"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data.lower()).first()
        
        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'error')
                return redirect(url_for('auth.login'))
            
            # Update last login time
            user.last_login_at = datetime.utcnow()
            db.session.commit()
            
            # Log user in
            login_user(user, remember=form.remember_me.data)
            
            # Start trial if not already started and no subscription
            if not user.trial_started_at and not user.has_active_subscription():
                user.start_trial()
                db.session.commit()
                flash('Welcome! Your 7-day free trial has started.', 'success')
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard.index')
            
            flash(f'Welcome back, {user.first_name}!', 'success')
            return redirect(next_page)
        else:
            flash('Invalid email or password.', 'error')
    
    return render_template('auth/login.html', title='Sign In', form=form)

@bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration route"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = RegistrationForm()
    if form.validate_on_submit():
        # Create new user
        user = User(
            email=form.email.data.lower(),
            first_name=form.first_name.data.strip(),
            last_name=form.last_name.data.strip()
        )
        user.set_password(form.password.data)
        
        # Add to database
        db.session.add(user)
        db.session.commit()
        
        # Create user settings
        user_settings = UserSettings(user_id=user.id)
        db.session.add(user_settings)
        db.session.commit()
        
        # Send verification email
        try:
            send_verification_email(user)
            flash('Registration successful! Please check your email to verify your account.', 'success')
        except Exception as e:
            current_app.logger.error(f'Failed to send verification email: {e}')
            flash('Registration successful! However, we could not send the verification email. Please contact support.', 'warning')
        
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', title='Create Account', form=form)

@bp.route('/logout')
@login_required
def logout():
    """User logout route"""
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('main.index'))

@bp.route('/verify-email/<token>')
def verify_email(token):
    """Email verification route"""
    try:
        data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
        user_id = data.get('verify_email')
        user = User.query.get(user_id)
        
        if not user:
            flash('Invalid verification link.', 'error')
            return redirect(url_for('auth.login'))
        
        if user.is_verified:
            flash('Your email is already verified.', 'info')
            return redirect(url_for('auth.login'))
        
        if user.verify_email_token(token):
            db.session.commit()
            flash('Your email has been verified successfully! You can now log in.', 'success')
        else:
            flash('Invalid or expired verification link.', 'error')
    
    except jwt.ExpiredSignatureError:
        flash('The verification link has expired. Please request a new one.', 'error')
    except jwt.InvalidTokenError:
        flash('Invalid verification link.', 'error')
    
    return redirect(url_for('auth.login'))

@bp.route('/resend-verification', methods=['GET', 'POST'])
def resend_verification():
    """Resend email verification"""
    if current_user.is_authenticated and current_user.is_verified:
        return redirect(url_for('dashboard.index'))
    
    form = EmailVerificationForm()
    if form.validate_on_submit():
        if current_user.is_authenticated:
            try:
                send_verification_email(current_user)
                flash('Verification email sent! Please check your inbox.', 'success')
            except Exception as e:
                current_app.logger.error(f'Failed to send verification email: {e}')
                flash('Failed to send verification email. Please try again later.', 'error')
        else:
            flash('Please log in to resend verification email.', 'error')
            return redirect(url_for('auth.login'))
    
    return render_template('auth/resend_verification.html', title='Resend Verification', form=form)

@bp.route('/reset-password-request', methods=['GET', 'POST'])
def reset_password_request():
    """Password reset request route"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = RequestPasswordResetForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data.lower()).first()
        if user:
            try:
                send_password_reset_email(user)
                flash('Password reset instructions have been sent to your email.', 'info')
            except Exception as e:
                current_app.logger.error(f'Failed to send password reset email: {e}')
                flash('Failed to send password reset email. Please try again later.', 'error')
        else:
            # Don't reveal if email exists or not for security
            flash('If an account with this email exists, password reset instructions have been sent.', 'info')
        
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password_request.html', title='Reset Password', form=form)

@bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Password reset route"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    try:
        data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
        user_id = data.get('reset_password')
        user = User.query.get(user_id)
        
        if not user:
            flash('Invalid reset link.', 'error')
            return redirect(url_for('auth.login'))
    
    except jwt.ExpiredSignatureError:
        flash('The reset link has expired. Please request a new one.', 'error')
        return redirect(url_for('auth.reset_password_request'))
    except jwt.InvalidTokenError:
        flash('Invalid reset link.', 'error')
        return redirect(url_for('auth.login'))
    
    form = ResetPasswordForm()
    if form.validate_on_submit():
        user.set_password(form.password.data)
        db.session.commit()
        flash('Your password has been reset successfully. You can now log in.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password.html', title='Reset Password', form=form)

@bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password route for logged-in users"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.password.data)
            db.session.commit()
            flash('Your password has been changed successfully.', 'success')
            return redirect(url_for('dashboard.settings'))
        else:
            flash('Current password is incorrect.', 'error')
    
    return render_template('auth/change_password.html', title='Change Password', form=form)
