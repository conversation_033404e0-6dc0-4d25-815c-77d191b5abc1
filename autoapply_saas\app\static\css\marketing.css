/* Marketing Website Styles for AutoApply.co.nz */

/* Custom Properties */
:root {
    --primary-color: #0066cc;
    --secondary-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --purple-color: #6f42c1;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    
    --gradient-primary: linear-gradient(135deg, #0066cc 0%, #004499 100%);
    --gradient-secondary: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Base Styles */
body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--dark-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 700;
    line-height: 1.2;
}

/* Utility Classes */
.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.text-purple {
    color: var(--purple-color) !important;
}

.min-vh-75 {
    min-height: 75vh;
}

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.hero-image {
    position: relative;
    z-index: 2;
}

.dashboard-preview {
    position: relative;
}

.dashboard-preview img {
    border: 3px solid rgba(255, 255, 255, 0.2);
}

/* Floating Notifications */
.floating-notification {
    position: absolute;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.5s ease;
    z-index: 10;
}

.notification-1 {
    top: 20%;
    right: -10%;
    animation: float 3s ease-in-out infinite;
}

.notification-2 {
    top: 50%;
    left: -15%;
    animation: float 3s ease-in-out infinite 1s;
}

.notification-3 {
    bottom: 20%;
    right: -5%;
    animation: float 3s ease-in-out infinite 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Trust Section */
.trust-section img {
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.trust-section img:hover {
    filter: grayscale(0%);
}

/* Problem Section */
.problem-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: var(--border-radius-lg);
}

.problem-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.problem-icon {
    opacity: 0.8;
}

/* Solution Section */
.solution-visual {
    position: relative;
}

.automation-flow {
    max-width: 400px;
    margin: 0 auto;
}

.flow-step {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.flow-step:hover {
    transform: translateX(10px);
    box-shadow: var(--box-shadow-lg);
}

.flow-step.active {
    border-left: 4px solid var(--primary-color);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.step-content p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: #666;
}

.flow-arrow {
    text-align: center;
    margin: 0.5rem 0;
}

/* How It Works Section */
.step-card {
    transition: transform 0.3s ease;
}

.step-card:hover {
    transform: translateY(-10px);
}

.step-icon {
    position: relative;
    margin-bottom: 2rem;
}

.icon-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: var(--box-shadow-lg);
}

.step-number {
    position: absolute;
    top: -10px;
    right: 50%;
    transform: translateX(50%);
    background: var(--warning-color);
    color: var(--dark-color);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

/* Features Section */
.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    opacity: 0.8;
}

/* Testimonials Section */
.testimonial-card {
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.testimonial-rating {
    font-size: 0.9rem;
}

.testimonial-author img {
    object-fit: cover;
}

/* Pricing Section */
.pricing-card {
    border: 2px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-lg);
}

.pricing-featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
}

.pricing-price {
    margin: 1rem 0;
}

.feature-item {
    font-size: 0.9rem;
}

/* FAQ Section */
.accordion-button {
    font-weight: 600;
    color: var(--dark-color);
}

.accordion-button:not(.collapsed) {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(0, 102, 204, 0.25);
}

/* CTA Section */
.cta-section {
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
}

.cta-buttons .btn {
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .floating-notification {
        display: none;
    }
    
    .pricing-featured {
        transform: none;
        margin-top: 2rem;
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .step-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-stats .row {
        text-align: center;
    }
    
    .hero-stats .col-4 {
        margin-bottom: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #004499;
}

/* Print Styles */
@media print {
    .hero-section,
    .cta-section,
    .floating-notification {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
}
