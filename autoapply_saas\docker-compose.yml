version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: autoapply_dev
      POSTGRES_USER: autoapply
      POSTGRES_PASSWORD: autoapply_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autoapply"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flask Web Application
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=*************************************************/autoapply_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
    volumes:
      - .:/app
      - uploads_data:/app/uploads
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: flask run --host=0.0.0.0 --port=5000 --debug

  # Celery Worker for background tasks
  worker:
    build: .
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=*************************************************/autoapply_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CHROME_DRIVER_PATH=/usr/local/bin/chromedriver
      - CHROME_BINARY_PATH=/usr/bin/google-chrome
    volumes:
      - .:/app
      - uploads_data:/app/uploads
      - chrome_data:/app/chrome_data
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.celery worker --loglevel=info

  # Celery Beat for scheduled tasks
  beat:
    build: .
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=*************************************************/autoapply_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - .:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.celery beat --loglevel=info

  # Flower for Celery monitoring (optional)
  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    command: celery -A app.celery flower --port=5555

volumes:
  postgres_data:
  redis_data:
  uploads_data:
  chrome_data:
