{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Analytics & Reports</h1>
            <div class="d-flex gap-2">
                <select class="form-select" id="timeRangeSelect" onchange="changeTimeRange()">
                    <option value="7" {{ 'selected' if days == 7 else '' }}>Last 7 days</option>
                    <option value="30" {{ 'selected' if days == 30 else '' }}>Last 30 days</option>
                    <option value="90" {{ 'selected' if days == 90 else '' }}>Last 90 days</option>
                </select>
                <button class="btn btn-outline-primary" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i>Export Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">New Users</div>
                        <div class="h4 mb-0">{{ user_analytics.trial_users or 0 }}</div>
                        <small class="text-success">
                            {{ user_analytics.conversion_rate or 0 }}% conversion rate
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-dollar-sign text-success fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Monthly Revenue</div>
                        <div class="h4 mb-0">${{ revenue_analytics.mrr or 0 }}</div>
                        <small class="text-success">
                            +{{ revenue_analytics.new_subscriptions or 0 }} new subs
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-paper-plane text-info fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Applications</div>
                        <div class="h4 mb-0">{{ application_analytics.total_applications or 0 }}</div>
                        <small class="text-success">
                            {{ application_analytics.success_rate or 0 }}% success rate
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-chart-line text-warning fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Churn Rate</div>
                        <div class="h4 mb-0">{{ revenue_analytics.churn_rate or 0 }}%</div>
                        <small class="text-danger">
                            {{ revenue_analytics.canceled_subscriptions or 0 }} cancellations
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>User Growth & Applications
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart me-2"></i>Revenue Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>System Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-primary">{{ system_performance.avg_session_duration or 0 }}</div>
                            <small class="text-muted">Avg Session Duration (min)</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-success">{{ system_performance.avg_applications_per_session or 0 }}</div>
                            <small class="text-muted">Avg Applications/Session</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-info">{{ system_performance.total_sessions or 0 }}</div>
                            <small class="text-muted">Total Sessions</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-warning">{{ system_performance.error_rate or 0 }}%</div>
                            <small class="text-muted">Error Rate</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6 class="fw-bold">Session Success Rate</h6>
                        <div class="progress mb-2" style="height: 8px;">
                            {% set success_rate = 100 - (system_performance.error_rate or 0) %}
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ success_rate }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted">
                            <span>{{ system_performance.total_sessions - system_performance.failed_sessions or 0 }} successful</span>
                            <span>{{ success_rate|round }}% success rate</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>Top Companies
                </h5>
            </div>
            <div class="card-body">
                {% if application_analytics.top_companies %}
                    {% for company in application_analytics.top_companies[:8] %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">{{ company.name }}</span>
                        <span class="badge bg-primary">{{ company.count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-building text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No application data available</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Detailed Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>User Registration Trends
                </h5>
            </div>
            <div class="card-body">
                {% if user_analytics.registrations %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Registrations</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reg in user_analytics.registrations[-7:] %}
                                <tr>
                                    <td>{{ reg.date }}</td>
                                    <td><span class="badge bg-primary">{{ reg.count }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No registration data available</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-paper-plane me-2"></i>Application Trends
                </h5>
            </div>
            <div class="card-body">
                {% if application_analytics.applications %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Applications</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for app in application_analytics.applications[-7:] %}
                                <tr>
                                    <td>{{ app.date }}</td>
                                    <td><span class="badge bg-success">{{ app.count }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-line text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No application data available</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initializeGrowthChart();
    initializeRevenueChart();
});

function initializeGrowthChart() {
    const ctx = document.getElementById('growthChart').getContext('2d');
    
    // Prepare data from backend
    const registrationData = {{ user_analytics.registrations | tojson if user_analytics.registrations else '[]' }};
    const applicationData = {{ application_analytics.applications | tojson if application_analytics.applications else '[]' }};
    
    const labels = registrationData.map(item => item.date);
    const regCounts = registrationData.map(item => item.count);
    const appCounts = applicationData.map(item => item.count);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'New Users',
                    data: regCounts,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: 'Applications',
                    data: appCounts,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'New Users'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Applications'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function initializeRevenueChart() {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    const data = {
        labels: ['Active Subscriptions', 'Trial Users', 'Churned'],
        datasets: [{
            data: [
                {{ revenue_analytics.active_subscriptions or 0 }},
                {{ user_analytics.trial_users or 0 }},
                {{ revenue_analytics.canceled_subscriptions or 0 }}
            ],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(220, 53, 69, 1)'
            ],
            borderWidth: 2
        }]
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

function changeTimeRange() {
    const select = document.getElementById('timeRangeSelect');
    const days = select.value;
    window.location.href = `{{ url_for('admin.analytics') }}?days=${days}`;
}

function exportReport() {
    // Implementation for report export
    showAdminToast('Report export feature coming soon!', 'info');
}
</script>
{% endblock %}
