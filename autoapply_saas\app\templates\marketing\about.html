{% extends "base.html" %}

{% block title %}About Us - AutoApply.co.nz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/marketing.css') }}">
{% endblock %}

{% block content %}
<!-- About Hero -->
<section class="about-hero py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">About AutoApply.co.nz</h1>
                <p class="lead mb-4">
                    We're on a mission to revolutionize job searching in New Zealand by making it 
                    more efficient, effective, and accessible for everyone.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Our Story -->
<section class="our-story py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="h1 fw-bold mb-4">Our Story</h2>
                <p class="lead text-muted mb-4">
                    AutoApply.co.nz was born from the frustration of spending countless hours 
                    manually applying to jobs with little success.
                </p>
                <p class="mb-4">
                    Our founders experienced firsthand the challenges of job hunting in New Zealand - 
                    the repetitive application process, the low response rates, and the time-consuming 
                    nature of traditional job searching. They realized that technology could solve 
                    these problems and level the playing field for all job seekers.
                </p>
                <p class="mb-4">
                    After months of development and testing, AutoApply.co.nz was launched to help 
                    Kiwis land their dream jobs faster and more efficiently than ever before.
                </p>
            </div>
            <div class="col-lg-6">
                <div class="story-visual text-center">
                    <img src="{{ url_for('static', filename='images/about-story.png') }}" 
                         alt="Our Story" class="img-fluid rounded-3 shadow-lg">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Mission -->
<section class="our-mission py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center text-center mb-5">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-4">Our Mission</h2>
                <p class="lead text-muted">
                    To democratize job searching by providing powerful automation tools that 
                    help every job seeker compete effectively in today's market.
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="mission-card text-center p-4">
                    <div class="mission-icon mb-3">
                        <i class="fas fa-users text-primary fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Accessibility</h5>
                    <p class="text-muted">
                        Making advanced job search technology accessible to everyone, 
                        regardless of technical expertise or budget.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="mission-card text-center p-4">
                    <div class="mission-icon mb-3">
                        <i class="fas fa-rocket text-success fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Efficiency</h5>
                    <p class="text-muted">
                        Streamlining the job application process to save time and 
                        increase the number of quality applications.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="mission-card text-center p-4">
                    <div class="mission-icon mb-3">
                        <i class="fas fa-chart-line text-warning fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Success</h5>
                    <p class="text-muted">
                        Helping job seekers achieve better outcomes with higher 
                        response rates and more interview opportunities.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Values -->
<section class="our-values py-5">
    <div class="container">
        <div class="row justify-content-center text-center mb-5">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-4">Our Values</h2>
                <p class="lead text-muted">
                    The principles that guide everything we do
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="value-card d-flex p-4 bg-white rounded-3 shadow-sm">
                    <div class="value-icon me-4">
                        <i class="fas fa-shield-alt text-primary fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">Trust & Security</h5>
                        <p class="text-muted mb-0">
                            We protect your data and account with enterprise-grade security 
                            and transparent practices.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="value-card d-flex p-4 bg-white rounded-3 shadow-sm">
                    <div class="value-icon me-4">
                        <i class="fas fa-lightbulb text-warning fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">Innovation</h5>
                        <p class="text-muted mb-0">
                            We continuously improve our technology to stay ahead of 
                            changing job market trends.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="value-card d-flex p-4 bg-white rounded-3 shadow-sm">
                    <div class="value-icon me-4">
                        <i class="fas fa-heart text-danger fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">Customer Focus</h5>
                        <p class="text-muted mb-0">
                            Every feature we build is designed with our users' success 
                            and satisfaction in mind.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="value-card d-flex p-4 bg-white rounded-3 shadow-sm">
                    <div class="value-icon me-4">
                        <i class="fas fa-balance-scale text-info fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-2">Fairness</h5>
                        <p class="text-muted mb-0">
                            We believe everyone deserves equal opportunities to find 
                            meaningful employment.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="team-section py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center text-center mb-5">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-4">Meet Our Team</h2>
                <p class="lead text-muted">
                    The passionate people behind AutoApply.co.nz
                </p>
            </div>
        </div>
        
        <div class="row g-4 justify-content-center">
            <div class="col-lg-4 col-md-6">
                <div class="team-card text-center p-4 bg-white rounded-3 shadow-sm">
                    <div class="team-photo mb-3">
                        <img src="{{ url_for('static', filename='images/team/founder-1.jpg') }}" 
                             alt="Alex Chen" class="rounded-circle" width="120" height="120">
                    </div>
                    <h5 class="fw-bold mb-1">Alex Chen</h5>
                    <p class="text-primary mb-2">Co-Founder & CEO</p>
                    <p class="text-muted small">
                        Former software engineer with 8+ years experience in automation 
                        and AI technologies.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-primary me-2"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-info"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="team-card text-center p-4 bg-white rounded-3 shadow-sm">
                    <div class="team-photo mb-3">
                        <img src="{{ url_for('static', filename='images/team/founder-2.jpg') }}" 
                             alt="Sarah Williams" class="rounded-circle" width="120" height="120">
                    </div>
                    <h5 class="fw-bold mb-1">Sarah Williams</h5>
                    <p class="text-primary mb-2">Co-Founder & CTO</p>
                    <p class="text-muted small">
                        Expert in machine learning and web automation with a passion 
                        for solving real-world problems.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-primary me-2"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-dark"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="team-card text-center p-4 bg-white rounded-3 shadow-sm">
                    <div class="team-photo mb-3">
                        <img src="{{ url_for('static', filename='images/team/founder-3.jpg') }}" 
                             alt="Mike Johnson" class="rounded-circle" width="120" height="120">
                    </div>
                    <h5 class="fw-bold mb-1">Mike Johnson</h5>
                    <p class="text-primary mb-2">Head of Customer Success</p>
                    <p class="text-muted small">
                        Dedicated to ensuring every user achieves their job search goals 
                        with our platform.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-primary me-2"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-info"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Company Stats -->
<section class="company-stats py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-5">AutoApply by the Numbers</h2>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card text-center p-4">
                    <div class="stat-number h1 fw-bold text-primary mb-2">10,000+</div>
                    <div class="stat-label text-muted">Applications Sent</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card text-center p-4">
                    <div class="stat-number h1 fw-bold text-success mb-2">500+</div>
                    <div class="stat-label text-muted">Jobs Landed</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card text-center p-4">
                    <div class="stat-number h1 fw-bold text-warning mb-2">1,200+</div>
                    <div class="stat-label text-muted">Happy Users</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card text-center p-4">
                    <div class="stat-number h1 fw-bold text-info mb-2">95%</div>
                    <div class="stat-label text-muted">Customer Satisfaction</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="about-cta py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-4">Ready to Join Our Success Stories?</h2>
                <p class="lead mb-4">
                    Start your free trial today and experience the AutoApply difference.
                </p>
                
                <div class="cta-buttons">
                    <a href="{{ url_for('auth.register') }}" class="btn btn-warning btn-lg me-3 px-5 py-3">
                        <i class="fas fa-rocket me-2"></i>Start Free Trial
                    </a>
                    <a href="{{ url_for('main.contact') }}" class="btn btn-outline-light btn-lg px-5 py-3">
                        <i class="fas fa-envelope me-2"></i>Contact Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
// Animate stats on scroll
function animateStats() {
    const stats = document.querySelectorAll('.stat-number');
    
    stats.forEach(stat => {
        const finalValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
        const duration = 2000; // 2 seconds
        const increment = finalValue / (duration / 16); // 60fps
        let currentValue = 0;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            
            const suffix = stat.textContent.includes('+') ? '+' : 
                          stat.textContent.includes('%') ? '%' : '';
            stat.textContent = Math.floor(currentValue).toLocaleString() + suffix;
        }, 16);
    });
}

// Trigger animation when stats section is visible
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateStats();
            observer.unobserve(entry.target);
        }
    });
});

const statsSection = document.querySelector('.company-stats');
if (statsSection) {
    observer.observe(statsSection);
}
</script>
{% endblock %}
