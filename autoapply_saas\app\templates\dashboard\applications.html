{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Job Applications</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#filterModal">
                    <i class="fas fa-filter me-2"></i>Filter
                </button>
                <button class="btn btn-outline-primary" onclick="exportApplications()">
                    <i class="fas fa-download me-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-paper-plane text-primary fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ applications.total }}</h4>
                <p class="text-muted mb-0">Total Applications</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-check-circle text-success fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ applications.items | selectattr('application_status', 'equalto', 'submitted') | list | length }}</h4>
                <p class="text-muted mb-0">Successful</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-robot text-info fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ applications.items | selectattr('cover_letter_generated', 'equalto', true) | list | length }}</h4>
                <p class="text-muted mb-0">AI Cover Letters</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-calendar text-warning fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ current_user.get_applications_today() }}</h4>
                <p class="text-muted mb-0">Today</p>
            </div>
        </div>
    </div>
</div>

<!-- Applications Table -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Application History
                </h5>
            </div>
            <div class="card-body">
                {% if applications.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Job Details</th>
                                    <th>Company</th>
                                    <th>Status</th>
                                    <th>Applied Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for app in applications.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-start">
                                            <div class="flex-grow-1">
                                                <div class="fw-bold">{{ app.job_title }}</div>
                                                {% if app.job_url %}
                                                    <a href="{{ app.job_url }}" target="_blank" class="small text-primary text-decoration-none">
                                                        <i class="fas fa-external-link-alt me-1"></i>View Job
                                                    </a>
                                                {% endif %}
                                                <div class="mt-1">
                                                    {% if app.cover_letter_generated %}
                                                        <span class="badge bg-success bg-opacity-10 text-success">
                                                            <i class="fas fa-robot me-1"></i>AI Cover Letter
                                                        </span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ app.company_name }}</div>
                                    </td>
                                    <td>
                                        {% if app.application_status == 'submitted' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Submitted
                                            </span>
                                        {% elif app.application_status == 'failed' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Failed
                                            </span>
                                        {% elif app.application_status == 'rate_limited' %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>Rate Limited
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                {{ app.application_status.title() }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ app.applied_at.strftime('%b %d, %Y') }}</div>
                                        <small class="text-muted">{{ app.applied_at.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="viewApplication({{ app.id }})">
                                                        <i class="fas fa-eye me-2"></i>View Details
                                                    </a>
                                                </li>
                                                {% if app.cover_letter_content %}
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="viewCoverLetter({{ app.id }})">
                                                        <i class="fas fa-file-alt me-2"></i>View Cover Letter
                                                    </a>
                                                </li>
                                                {% endif %}
                                                {% if app.job_url %}
                                                <li>
                                                    <a class="dropdown-item" href="{{ app.job_url }}" target="_blank">
                                                        <i class="fas fa-external-link-alt me-2"></i>Open Job Posting
                                                    </a>
                                                </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if applications.pages > 1 %}
                    <nav aria-label="Applications pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if applications.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.applications', page=applications.prev_num) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in applications.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != applications.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('dashboard.applications', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if applications.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('dashboard.applications', page=applications.next_num) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox text-muted fa-3x mb-4"></i>
                        <h4 class="text-muted">No Applications Yet</h4>
                        <p class="text-muted mb-4">Start your first automation session to see applications here.</p>
                        <a href="{{ url_for('dashboard.automation') }}" class="btn btn-primary">
                            <i class="fas fa-robot me-2"></i>Start Automation
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-filter me-2"></i>Filter Applications
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="filterForm">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="">All Statuses</option>
                            <option value="submitted">Submitted</option>
                            <option value="failed">Failed</option>
                            <option value="rate_limited">Rate Limited</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Date Range</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_from" placeholder="From">
                            </div>
                            <div class="col-6">
                                <input type="date" class="form-control" name="date_to" placeholder="To">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Company</label>
                        <input type="text" class="form-control" name="company" placeholder="Filter by company name">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="ai_cover_letter" id="aiCoverLetterFilter">
                            <label class="form-check-label" for="aiCoverLetterFilter">
                                Only applications with AI cover letters
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">Clear Filters</button>
                <button type="button" class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
            </div>
        </div>
    </div>
</div>

<!-- Application Details Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Application Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="applicationDetails">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cover Letter Modal -->
<div class="modal fade" id="coverLetterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>Cover Letter
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="coverLetterContent" class="border rounded p-3" style="white-space: pre-wrap; font-family: 'Times New Roman', serif; line-height: 1.6;">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" onclick="copyCoverLetter()">
                    <i class="fas fa-copy me-2"></i>Copy to Clipboard
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
async function viewApplication(applicationId) {
    try {
        const response = await fetch(`/api/applications/${applicationId}`);
        const app = await response.json();
        
        const detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">Job Information</h6>
                    <p><strong>Title:</strong> ${app.job_title}</p>
                    <p><strong>Company:</strong> ${app.company_name}</p>
                    <p><strong>Applied:</strong> ${new Date(app.applied_at).toLocaleString()}</p>
                    <p><strong>Status:</strong> 
                        <span class="badge bg-${app.application_status === 'submitted' ? 'success' : 'danger'}">
                            ${app.application_status}
                        </span>
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold">Automation Details</h6>
                    <p><strong>Cover Letter:</strong> ${app.cover_letter_generated ? 'Generated by AI' : 'Not generated'}</p>
                    <p><strong>Session ID:</strong> ${app.automation_session_id || 'N/A'}</p>
                    ${app.job_url ? `<p><a href="${app.job_url}" target="_blank" class="btn btn-sm btn-outline-primary">View Job Posting</a></p>` : ''}
                </div>
            </div>
            ${app.job_description ? `
                <div class="mt-3">
                    <h6 class="fw-bold">Job Description</h6>
                    <div class="border rounded p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                        ${app.job_description}
                    </div>
                </div>
            ` : ''}
        `;
        
        document.getElementById('applicationDetails').innerHTML = detailsHtml;
        new bootstrap.Modal(document.getElementById('applicationModal')).show();
    } catch (error) {
        console.error('Error loading application details:', error);
        alert('Failed to load application details');
    }
}

async function viewCoverLetter(applicationId) {
    try {
        const response = await fetch(`/api/applications/${applicationId}`);
        const app = await response.json();
        
        if (app.cover_letter_content) {
            document.getElementById('coverLetterContent').textContent = app.cover_letter_content;
            new bootstrap.Modal(document.getElementById('coverLetterModal')).show();
        } else {
            alert('No cover letter available for this application');
        }
    } catch (error) {
        console.error('Error loading cover letter:', error);
        alert('Failed to load cover letter');
    }
}

function copyCoverLetter() {
    const content = document.getElementById('coverLetterContent').textContent;
    navigator.clipboard.writeText(content).then(() => {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast position-fixed bottom-0 end-0 m-3';
        toast.innerHTML = `
            <div class="toast-body bg-success text-white">
                Cover letter copied to clipboard!
            </div>
        `;
        document.body.appendChild(toast);
        new bootstrap.Toast(toast).show();
        setTimeout(() => toast.remove(), 3000);
    });
}

function applyFilters() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (const [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    window.location.href = `${window.location.pathname}?${params.toString()}`;
}

function clearFilters() {
    window.location.href = window.location.pathname;
}

function exportApplications() {
    // Implementation for exporting applications to CSV
    window.location.href = '/api/applications/export';
}
</script>
{% endblock %}
