{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">Admin Settings</h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- System Configuration -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>System Configuration
                </h5>
            </div>
            <div class="card-body">
                <form id="systemConfigForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Max Concurrent Sessions</label>
                            <input type="number" class="form-control" name="max_concurrent_sessions" 
                                   value="5" min="1" max="20">
                            <small class="form-text text-muted">Maximum number of automation sessions that can run simultaneously</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Max Applications Per Day</label>
                            <input type="number" class="form-control" name="max_applications_per_day" 
                                   value="50" min="10" max="200">
                            <small class="form-text text-muted">Daily application limit per user</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Free Trial Days</label>
                            <input type="number" class="form-control" name="free_trial_days" 
                                   value="7" min="1" max="30">
                            <small class="form-text text-muted">Number of days for free trial</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Free Trial Applications</label>
                            <input type="number" class="form-control" name="free_trial_applications" 
                                   value="10" min="1" max="50">
                            <small class="form-text text-muted">Number of applications allowed in free trial</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Maintenance Mode</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenanceMode">
                            <label class="form-check-label" for="maintenanceMode">
                                Enable maintenance mode (prevents new automation sessions)
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Email Settings -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2"></i>Email Configuration
                </h5>
            </div>
            <div class="card-body">
                <form id="emailConfigForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SMTP Server</label>
                            <input type="text" class="form-control" name="smtp_server" 
                                   placeholder="smtp.gmail.com">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">SMTP Port</label>
                            <input type="number" class="form-control" name="smtp_port" 
                                   value="587" min="1" max="65535">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">From Email</label>
                            <input type="email" class="form-control" name="from_email" 
                                   placeholder="<EMAIL>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">From Name</label>
                            <input type="text" class="form-control" name="from_name" 
                                   value="AutoApply.co.nz">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="email_notifications_enabled" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                Enable email notifications
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="testEmailConfig()">
                            <i class="fas fa-paper-plane me-2"></i>Test Email
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Email Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Security Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="securityConfigForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Session Timeout (minutes)</label>
                            <input type="number" class="form-control" name="session_timeout" 
                                   value="60" min="15" max="480">
                            <small class="form-text text-muted">User session timeout in minutes</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Max Login Attempts</label>
                            <input type="number" class="form-control" name="max_login_attempts" 
                                   value="5" min="3" max="10">
                            <small class="form-text text-muted">Maximum failed login attempts before lockout</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Allowed Admin Domains</label>
                        <textarea class="form-control" name="admin_domains" rows="3" 
                                  placeholder="seek.co.nz&#10;autoapply.co.nz">seek.co.nz</textarea>
                        <small class="form-text text-muted">Email domains allowed for admin access (one per line)</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="require_email_verification" id="requireVerification" checked>
                            <label class="form-check-label" for="requireVerification">
                                Require email verification for new accounts
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Security Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- System Status -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Application Version</span>
                    <span class="badge bg-primary">v1.0.0</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database Status</span>
                    <span class="badge bg-success">Connected</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Redis Cache</span>
                    <span class="badge bg-success">Connected</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Celery Workers</span>
                    <span class="badge bg-success">3 Active</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Uptime</span>
                    <span class="text-muted">2 days, 14 hours</span>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning" onclick="restartServices()">
                        <i class="fas fa-redo me-2"></i>Restart Services
                    </button>
                    
                    <button class="btn btn-outline-info" onclick="clearCache()">
                        <i class="fas fa-broom me-2"></i>Clear Cache
                    </button>
                    
                    <button class="btn btn-outline-secondary" onclick="backupDatabase()">
                        <i class="fas fa-database me-2"></i>Backup Database
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Maintenance Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Maintenance Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="cleanupOldLogs()">
                        <i class="fas fa-trash me-2"></i>Cleanup Old Logs
                    </button>
                    
                    <button class="btn btn-outline-success" onclick="optimizeDatabase()">
                        <i class="fas fa-database me-2"></i>Optimize Database
                    </button>
                    
                    <button class="btn btn-outline-info" onclick="generateReport()">
                        <i class="fas fa-chart-bar me-2"></i>Generate System Report
                    </button>
                    
                    <button class="btn btn-outline-warning" onclick="sendTestNotifications()">
                        <i class="fas fa-bell me-2"></i>Test Notifications
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3 text-center">
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">1,234</div>
                            <small class="text-muted">Total Users</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">567</div>
                            <small class="text-muted">Active Subs</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">89</div>
                            <small class="text-muted">Today Apps</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">3</div>
                            <small class="text-muted">Running</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// System configuration form
document.getElementById('systemConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    showAdminToast('System configuration saved successfully!', 'success');
});

// Email configuration form
document.getElementById('emailConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    showAdminToast('Email configuration saved successfully!', 'success');
});

// Security configuration form
document.getElementById('securityConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();
    showAdminToast('Security settings saved successfully!', 'success');
});

function testEmailConfig() {
    showAdminToast('Sending test email...', 'info');
    
    // Simulate email test
    setTimeout(() => {
        showAdminToast('Test email sent successfully!', 'success');
    }, 2000);
}

function restartServices() {
    if (confirm('Are you sure you want to restart system services? This may cause brief downtime.')) {
        showAdminToast('Restarting services...', 'warning');
        // Implementation would restart services
    }
}

function clearCache() {
    if (confirm('Are you sure you want to clear the system cache?')) {
        showAdminToast('Cache cleared successfully!', 'success');
        // Implementation would clear cache
    }
}

function backupDatabase() {
    showAdminToast('Starting database backup...', 'info');
    // Implementation would trigger database backup
    setTimeout(() => {
        showAdminToast('Database backup completed!', 'success');
    }, 3000);
}

function cleanupOldLogs() {
    if (confirm('Are you sure you want to cleanup old system logs?')) {
        showAdminToast('Cleaning up old logs...', 'info');
        // Implementation would cleanup logs
        setTimeout(() => {
            showAdminToast('Old logs cleaned up successfully!', 'success');
        }, 2000);
    }
}

function optimizeDatabase() {
    if (confirm('Are you sure you want to optimize the database? This may take a few minutes.')) {
        showAdminToast('Optimizing database...', 'info');
        // Implementation would optimize database
        setTimeout(() => {
            showAdminToast('Database optimization completed!', 'success');
        }, 5000);
    }
}

function generateReport() {
    showAdminToast('Generating system report...', 'info');
    // Implementation would generate and download report
    setTimeout(() => {
        showAdminToast('System report generated!', 'success');
    }, 3000);
}

function sendTestNotifications() {
    showAdminToast('Sending test notifications...', 'info');
    // Implementation would send test notifications
    setTimeout(() => {
        showAdminToast('Test notifications sent!', 'success');
    }, 2000);
}
</script>
{% endblock %}
