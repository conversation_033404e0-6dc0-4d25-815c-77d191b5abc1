# Prometheus alert rules for AutoApply.co.nz
groups:
  - name: autoapply_alerts
    rules:
      # Application health alerts
      - alert: ApplicationDown
        expr: up{job="autoapply-web"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AutoApply application is down"
          description: "The AutoApply web application has been down for more than 1 minute."

      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for more than 5 minutes."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second for more than 2 minutes."

      # Database alerts
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "The PostgreSQL database has been down for more than 1 minute."

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }} for more than 5 minutes."

      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Database slow queries detected"
          description: "Database query efficiency is low: {{ $value | humanizePercentage }} for more than 10 minutes."

      # Redis alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value | humanizePercentage }} for more than 5 minutes."

      # System resource alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% for more than 5 minutes."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} for more than 5 minutes."

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} available for more than 5 minutes."

      # Celery worker alerts
      - alert: CeleryWorkersDown
        expr: celery_workers_total == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "No Celery workers available"
          description: "All Celery workers are down for more than 2 minutes."

      - alert: CeleryQueueBacklog
        expr: celery_queue_length > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Celery queue backlog"
          description: "Celery queue has {{ $value }} pending tasks for more than 10 minutes."

      - alert: CeleryTaskFailureRate
        expr: rate(celery_task_total{state="FAILURE"}[5m]) / rate(celery_task_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task failure rate"
          description: "Celery task failure rate is {{ $value | humanizePercentage }} for more than 5 minutes."

      # SSL certificate alerts
      - alert: SSLCertificateExpiringSoon
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}."

      - alert: SSLCertificateExpired
        expr: probe_ssl_earliest_cert_expiry - time() <= 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "SSL certificate expired"
          description: "SSL certificate for {{ $labels.instance }} has expired."

      # Security alerts
      - alert: HighFailedLoginRate
        expr: rate(http_requests_total{endpoint="/auth/login", status="401"}[5m]) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login rate"
          description: "Failed login rate is {{ $value }} per second for more than 2 minutes."

      - alert: SuspiciousActivity
        expr: rate(security_events_total{event_type="suspicious_activity"}[5m]) > 0.1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Suspicious activity detected"
          description: "Suspicious activity rate is {{ $value }} events per second."

      # Business metrics alerts
      - alert: LowUserRegistrations
        expr: rate(user_registrations_total[1h]) < 0.01
        for: 2h
        labels:
          severity: info
        annotations:
          summary: "Low user registration rate"
          description: "User registration rate is {{ $value }} per hour for more than 2 hours."

      - alert: HighSubscriptionChurnRate
        expr: rate(subscription_cancellations_total[24h]) / rate(subscription_activations_total[24h]) > 0.2
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "High subscription churn rate"
          description: "Subscription churn rate is {{ $value | humanizePercentage }} for the last 24 hours."

      # Automation service alerts
      - alert: AutomationServiceDown
        expr: automation_sessions_active == 0 and automation_queue_length > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Automation service appears down"
          description: "No active automation sessions but queue has {{ $labels.automation_queue_length }} pending jobs."

      - alert: HighAutomationFailureRate
        expr: rate(automation_sessions_total{status="failed"}[1h]) / rate(automation_sessions_total[1h]) > 0.3
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "High automation failure rate"
          description: "Automation failure rate is {{ $value | humanizePercentage }} for more than 30 minutes."
