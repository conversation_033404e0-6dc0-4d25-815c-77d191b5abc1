"""
Security routes for AutoApply.co.nz
Handles privacy requests, data exports, and security management
"""

from flask import render_template, request, jsonify, send_file, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from app.security import bp
from app.security.gdpr_compliance import gdpr_compliance
from app.security.rate_limiter import rate_limiter, rate_limit, log_security_event
from app.models import SystemLog, User
from app import db
import os

@bp.route('/privacy')
def privacy_policy():
    """Privacy policy page"""
    return render_template('security/privacy_policy.html', title='Privacy Policy')

@bp.route('/terms')
def terms_of_service():
    """Terms of service page"""
    return render_template('security/terms_of_service.html', title='Terms of Service')

@bp.route('/data-export', methods=['GET', 'POST'])
@login_required
@rate_limit('api', per_user=True)
def data_export():
    """Handle user data export requests (GDPR Article 20)"""
    if request.method == 'POST':
        try:
            export_format = request.form.get('format', 'json')
            
            if export_format not in ['json', 'csv']:
                flash('Invalid export format selected.', 'error')
                return redirect(url_for('security.data_export'))
            
            # Create data export
            result = gdpr_compliance.export_user_data(current_user.id, export_format)
            
            if result['success']:
                log_security_event(
                    'data_export_requested',
                    f"User {current_user.email} requested data export",
                    user_id=current_user.id,
                    additional_data={'format': export_format}
                )
                
                # Send file to user
                return send_file(
                    result['file_path'],
                    as_attachment=True,
                    download_name=f"my_data_export.{export_format}",
                    mimetype='application/octet-stream'
                )
            else:
                flash(f"Export failed: {result['error']}", 'error')
                
        except Exception as e:
            current_app.logger.error(f"Data export error: {e}")
            flash('An error occurred during export. Please try again.', 'error')
    
    return render_template('security/data_export.html', title='Export My Data')

@bp.route('/data-deletion', methods=['GET', 'POST'])
@login_required
@rate_limit('api', per_user=True)
def data_deletion():
    """Handle user data deletion requests (GDPR Article 17)"""
    if request.method == 'POST':
        try:
            confirmation = request.form.get('confirmation')
            
            if confirmation != 'DELETE MY ACCOUNT':
                flash('Please type "DELETE MY ACCOUNT" to confirm deletion.', 'error')
                return redirect(url_for('security.data_deletion'))
            
            # Create data export before deletion
            export_result = gdpr_compliance.export_user_data(current_user.id, 'json')
            
            # Delete user data
            result = gdpr_compliance.delete_user_data(current_user.id)
            
            if result['success']:
                log_security_event(
                    'account_deletion_requested',
                    f"User {current_user.email} requested account deletion",
                    user_id=current_user.id,
                    additional_data=result['summary']
                )
                
                flash('Your account and all associated data have been deleted.', 'success')
                
                # Log out user
                from flask_login import logout_user
                logout_user()
                
                return redirect(url_for('main.index'))
            else:
                flash(f"Deletion failed: {result['error']}", 'error')
                
        except Exception as e:
            current_app.logger.error(f"Data deletion error: {e}")
            flash('An error occurred during deletion. Please contact support.', 'error')
    
    return render_template('security/data_deletion.html', title='Delete My Account')

@bp.route('/consent-management')
@login_required
def consent_management():
    """Manage user consent preferences"""
    return render_template('security/consent_management.html', title='Privacy Preferences')

@bp.route('/api/consent', methods=['POST'])
@login_required
@rate_limit('api', per_user=True)
def update_consent():
    """Update user consent preferences"""
    try:
        data = request.get_json()
        
        # Update user settings based on consent
        if not current_user.user_settings:
            from app.models import UserSettings
            current_user.user_settings = UserSettings(user_id=current_user.id)
            db.session.add(current_user.user_settings)
        
        # Update consent preferences
        current_user.user_settings.email_notifications = data.get('email_notifications', False)
        current_user.user_settings.daily_summary_email = data.get('daily_summary_email', False)
        
        # Log consent changes
        log_security_event(
            'consent_updated',
            f"User {current_user.email} updated consent preferences",
            user_id=current_user.id,
            additional_data=data
        )
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Consent preferences updated'})
        
    except Exception as e:
        current_app.logger.error(f"Consent update error: {e}")
        return jsonify({'success': False, 'error': 'Failed to update preferences'}), 500

@bp.route('/security-log')
@login_required
def security_log():
    """Show user's security-related activity"""
    try:
        # Get user's security logs
        logs = SystemLog.query.filter(
            SystemLog.user_id == current_user.id,
            SystemLog.source == 'security'
        ).order_by(SystemLog.created_at.desc()).limit(50).all()
        
        return render_template('security/security_log.html', 
                             title='Security Activity', 
                             logs=logs)
        
    except Exception as e:
        current_app.logger.error(f"Security log error: {e}")
        flash('Unable to load security log.', 'error')
        return redirect(url_for('dashboard.settings'))

# Admin-only security routes

@bp.route('/admin/blocked-ips')
@login_required
def admin_blocked_ips():
    """Admin view of blocked IP addresses"""
    if not current_user.email.endswith('@seek.co.nz'):
        flash('Admin access required.', 'error')
        return redirect(url_for('main.index'))
    
    try:
        blocked_ips = rate_limiter.get_blocked_ips()
        return render_template('security/admin_blocked_ips.html',
                             title='Blocked IP Addresses',
                             blocked_ips=blocked_ips)
        
    except Exception as e:
        current_app.logger.error(f"Blocked IPs view error: {e}")
        flash('Unable to load blocked IPs.', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/admin/api/unblock-ip', methods=['POST'])
@login_required
@rate_limit('api', per_user=True)
def admin_unblock_ip():
    """Admin endpoint to unblock IP addresses"""
    if not current_user.email.endswith('@seek.co.nz'):
        return jsonify({'error': 'Admin access required'}), 403
    
    try:
        data = request.get_json()
        ip_address = data.get('ip_address')
        
        if not ip_address:
            return jsonify({'error': 'IP address required'}), 400
        
        success = rate_limiter.unblock_ip(ip_address)
        
        if success:
            log_security_event(
                'ip_unblocked',
                f"Admin {current_user.email} unblocked IP {ip_address}",
                user_id=current_user.id,
                additional_data={'ip_address': ip_address}
            )
            
            return jsonify({'success': True, 'message': f'IP {ip_address} unblocked'})
        else:
            return jsonify({'success': False, 'error': 'Failed to unblock IP'}), 500
            
    except Exception as e:
        current_app.logger.error(f"IP unblock error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/admin/privacy-report')
@login_required
def admin_privacy_report():
    """Admin privacy compliance report"""
    if not current_user.email.endswith('@seek.co.nz'):
        flash('Admin access required.', 'error')
        return redirect(url_for('main.index'))
    
    try:
        report = gdpr_compliance.generate_privacy_report()
        return render_template('security/admin_privacy_report.html',
                             title='Privacy Compliance Report',
                             report=report)
        
    except Exception as e:
        current_app.logger.error(f"Privacy report error: {e}")
        flash('Unable to generate privacy report.', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/admin/api/cleanup-expired-data', methods=['POST'])
@login_required
@rate_limit('api', per_user=True)
def admin_cleanup_expired_data():
    """Admin endpoint to cleanup expired data"""
    if not current_user.email.endswith('@seek.co.nz'):
        return jsonify({'error': 'Admin access required'}), 403
    
    try:
        result = gdpr_compliance.cleanup_expired_data()
        
        log_security_event(
            'data_cleanup_executed',
            f"Admin {current_user.email} executed data cleanup",
            user_id=current_user.id,
            additional_data=result
        )
        
        return jsonify({
            'success': True,
            'message': 'Data cleanup completed',
            'summary': result
        })
        
    except Exception as e:
        current_app.logger.error(f"Data cleanup error: {e}")
        return jsonify({'error': 'Cleanup failed'}), 500

@bp.route('/cookie-policy')
def cookie_policy():
    """Cookie policy page"""
    return render_template('security/cookie_policy.html', title='Cookie Policy')

@bp.route('/api/security-status')
@rate_limit('api')
def security_status():
    """Public endpoint for security status"""
    try:
        # Basic security status (no sensitive information)
        status = {
            'https_enabled': request.is_secure,
            'rate_limiting_active': bool(rate_limiter.redis),
            'security_headers_enabled': True,
            'last_updated': '2024-01-01T00:00:00Z'
        }
        
        return jsonify(status)
        
    except Exception as e:
        current_app.logger.error(f"Security status error: {e}")
        return jsonify({'error': 'Status unavailable'}), 500
