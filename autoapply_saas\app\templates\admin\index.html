{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Admin Dashboard</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync me-2"></i>Refresh
                </button>
                <a href="{{ url_for('admin.monitoring') }}" class="btn btn-outline-success">
                    <i class="fas fa-heartbeat me-2"></i>System Health
                </a>
            </div>
        </div>
    </div>
</div>

<!-- System Overview Cards -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Total Users</div>
                        <div class="h4 mb-0">{{ stats.users.total or 0 }}</div>
                        <small class="text-success">
                            <i class="fas fa-arrow-up me-1"></i>{{ stats.users.new_24h or 0 }} new today
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-crown text-success fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Active Subscriptions</div>
                        <div class="h4 mb-0">{{ stats.subscriptions.active or 0 }}</div>
                        <small class="text-success">
                            ${{ stats.subscriptions.revenue_monthly or 0 }} MRR
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-paper-plane text-info fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Applications Today</div>
                        <div class="h4 mb-0">{{ stats.applications.today or 0 }}</div>
                        <small class="text-muted">
                            {{ stats.applications.total or 0 }} total
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-robot text-warning fa-lg"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold text-muted small">Running Sessions</div>
                        <div class="h4 mb-0">{{ stats.sessions.running or 0 }}</div>
                        <small class="text-muted">
                            {{ stats.sessions.success_rate|round(1) or 0 }}% success rate
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>System Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart me-2"></i>User Distribution
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="userDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>Recent Users
                    </h5>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    {% for user in recent_users %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            {{ user.first_name[0].upper() }}{{ user.last_name[0].upper() }}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ user.first_name }} {{ user.last_name }}</div>
                            <small class="text-muted">{{ user.email }}</small>
                            <div class="small">
                                {% if user.has_active_subscription() %}
                                    <span class="badge bg-success">Premium</span>
                                {% elif user.is_trial_active() %}
                                    <span class="badge bg-info">Trial</span>
                                {% else %}
                                    <span class="badge bg-secondary">Free</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ user.created_at.strftime('%b %d') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-users text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No recent users</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-robot me-2"></i>Recent Sessions
                    </h5>
                    <a href="{{ url_for('admin.monitoring') }}" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                    {% for session in recent_sessions %}
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <div class="fw-bold small">
                                {% if session.status == 'completed' %}
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                {% elif session.status == 'failed' %}
                                    <i class="fas fa-times-circle text-danger me-1"></i>
                                {% elif session.status == 'running' %}
                                    <i class="fas fa-spinner fa-spin text-primary me-1"></i>
                                {% else %}
                                    <i class="fas fa-pause-circle text-muted me-1"></i>
                                {% endif %}
                                Session #{{ session.id }}
                            </div>
                            <small class="text-muted">
                                {{ session.applications_submitted or 0 }} applications
                            </small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ session.started_at.strftime('%H:%M') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-robot text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No recent sessions</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>System Alerts
                    </h5>
                    <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-primary btn-sm">
                        View Logs
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    {% for log in recent_logs[:5] %}
                    <div class="d-flex align-items-start mb-3">
                        <div class="flex-shrink-0 me-2">
                            {% if log.level == 'ERROR' %}
                                <i class="fas fa-times-circle text-danger"></i>
                            {% elif log.level == 'WARNING' %}
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            {% elif log.level == 'INFO' %}
                                <i class="fas fa-info-circle text-info"></i>
                            {% else %}
                                <i class="fas fa-circle text-muted"></i>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1">
                            <div class="small fw-bold">{{ log.level }}</div>
                            <div class="small text-muted">{{ log.message[:50] }}...</div>
                            <small class="text-muted">{{ log.created_at.strftime('%H:%M') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                        <div class="text-muted">No recent alerts</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initializeActivityChart();
    initializeUserDistributionChart();
});

function initializeActivityChart() {
    const ctx = document.getElementById('activityChart').getContext('2d');
    
    // Sample data - in production, this would come from the backend
    const data = {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
            {
                label: 'New Users',
                data: [12, 19, 3, 5, 2, 3, 7],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4
            },
            {
                label: 'Applications',
                data: [65, 59, 80, 81, 56, 55, 40],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.4
            }
        ]
    };
    
    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function initializeUserDistributionChart() {
    const ctx = document.getElementById('userDistributionChart').getContext('2d');
    
    const data = {
        labels: ['Premium', 'Trial', 'Free'],
        datasets: [{
            data: [{{ stats.subscriptions.active or 0 }}, {{ stats.users.trial or 0 }}, {{ (stats.users.total or 0) - (stats.subscriptions.active or 0) - (stats.users.trial or 0) }}],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(108, 117, 125, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(108, 117, 125, 1)'
            ],
            borderWidth: 2
        }]
    };
    
    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

function refreshDashboard() {
    // Add loading state
    document.body.classList.add('loading');
    
    // Reload the page
    setTimeout(() => {
        window.location.reload();
    }, 500);
}
</script>
{% endblock %}
