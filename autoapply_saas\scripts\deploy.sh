#!/bin/bash

# AutoApply.co.nz Deployment Script
# This script handles production deployment with zero downtime

set -euo pipefail

# Configuration
DEPLOY_DIR="/opt/autoapply"
BACKUP_DIR="/opt/autoapply/backups"
LOG_FILE="/var/log/autoapply-deploy.log"
COMPOSE_FILE="docker-compose.prod.yml"
HEALTH_CHECK_URL="http://localhost/health"
MAX_HEALTH_CHECK_ATTEMPTS=30
HEALTH_CHECK_INTERVAL=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
    fi
    
    # Check if git is installed
    if ! command -v git &> /dev/null; then
        error "Git is not installed"
    fi
    
    # Check if deployment directory exists
    if [[ ! -d "$DEPLOY_DIR" ]]; then
        error "Deployment directory $DEPLOY_DIR does not exist"
    fi
    
    success "Prerequisites check passed"
}

# Create backup before deployment
create_backup() {
    log "Creating backup before deployment..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/pre_deploy_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup database
    log "Backing up database..."
    docker-compose -f "$COMPOSE_FILE" exec -T db pg_dump -U autoapply autoapply_prod > "$backup_path/database.sql" || {
        warning "Database backup failed, but continuing deployment"
    }
    
    # Backup uploaded files
    log "Backing up uploaded files..."
    if [[ -d "$DEPLOY_DIR/uploads" ]]; then
        cp -r "$DEPLOY_DIR/uploads" "$backup_path/" || {
            warning "File backup failed, but continuing deployment"
        }
    fi
    
    # Backup environment file
    if [[ -f "$DEPLOY_DIR/.env.prod" ]]; then
        cp "$DEPLOY_DIR/.env.prod" "$backup_path/" || {
            warning "Environment file backup failed, but continuing deployment"
        }
    fi
    
    success "Backup created at $backup_path"
}

# Pull latest code
update_code() {
    log "Updating code from repository..."
    
    cd "$DEPLOY_DIR"
    
    # Stash any local changes
    git stash push -m "Auto-stash before deployment $(date)"
    
    # Pull latest changes
    git pull origin main || error "Failed to pull latest code"
    
    success "Code updated successfully"
}

# Update environment variables
update_environment() {
    log "Updating environment variables..."
    
    # Check if .env.prod exists
    if [[ ! -f "$DEPLOY_DIR/.env.prod" ]]; then
        warning ".env.prod file not found, creating from template"
        cp "$DEPLOY_DIR/.env.example" "$DEPLOY_DIR/.env.prod"
        warning "Please update .env.prod with production values"
    fi
    
    success "Environment variables updated"
}

# Pull Docker images
pull_images() {
    log "Pulling latest Docker images..."
    
    cd "$DEPLOY_DIR"
    docker-compose -f "$COMPOSE_FILE" pull || error "Failed to pull Docker images"
    
    success "Docker images pulled successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    cd "$DEPLOY_DIR"
    docker-compose -f "$COMPOSE_FILE" run --rm web flask db upgrade || error "Database migration failed"
    
    success "Database migrations completed"
}

# Deploy with zero downtime
deploy_application() {
    log "Deploying application with zero downtime..."
    
    cd "$DEPLOY_DIR"
    
    # Start new containers
    docker-compose -f "$COMPOSE_FILE" up -d --remove-orphans || error "Failed to start containers"
    
    success "Application deployed successfully"
}

# Health check
health_check() {
    log "Performing health check..."
    
    local attempt=1
    while [[ $attempt -le $MAX_HEALTH_CHECK_ATTEMPTS ]]; do
        log "Health check attempt $attempt/$MAX_HEALTH_CHECK_ATTEMPTS"
        
        if curl -f -s "$HEALTH_CHECK_URL" > /dev/null; then
            success "Health check passed"
            return 0
        fi
        
        if [[ $attempt -eq $MAX_HEALTH_CHECK_ATTEMPTS ]]; then
            error "Health check failed after $MAX_HEALTH_CHECK_ATTEMPTS attempts"
        fi
        
        sleep $HEALTH_CHECK_INTERVAL
        ((attempt++))
    done
}

# Clean up old Docker images
cleanup() {
    log "Cleaning up old Docker images..."
    
    # Remove unused images
    docker image prune -f || warning "Failed to clean up Docker images"
    
    # Remove old backups (keep last 10)
    if [[ -d "$BACKUP_DIR" ]]; then
        find "$BACKUP_DIR" -type d -name "pre_deploy_*" | sort -r | tail -n +11 | xargs rm -rf || {
            warning "Failed to clean up old backups"
        }
    fi
    
    success "Cleanup completed"
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    log "Sending deployment notification..."
    
    # Send to Slack if webhook is configured
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"AutoApply.co.nz Deployment $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" || warning "Failed to send Slack notification"
    fi
    
    # Log deployment event
    if [[ -n "${API_ENDPOINT:-}" ]] && [[ -n "${API_KEY:-}" ]]; then
        curl -X POST "$API_ENDPOINT/api/admin/deployments" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $API_KEY" \
            -d "{
                \"status\": \"$status\",
                \"message\": \"$message\",
                \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
                \"version\": \"$(git rev-parse HEAD)\"
            }" || warning "Failed to log deployment event"
    fi
}

# Rollback function
rollback() {
    log "Rolling back deployment..."
    
    # Find the latest backup
    local latest_backup=$(find "$BACKUP_DIR" -type d -name "pre_deploy_*" | sort -r | head -n 1)
    
    if [[ -z "$latest_backup" ]]; then
        error "No backup found for rollback"
    fi
    
    log "Rolling back to backup: $latest_backup"
    
    # Restore database
    if [[ -f "$latest_backup/database.sql" ]]; then
        log "Restoring database..."
        docker-compose -f "$COMPOSE_FILE" exec -T db psql -U autoapply -d autoapply_prod < "$latest_backup/database.sql" || {
            error "Database rollback failed"
        }
    fi
    
    # Restore files
    if [[ -d "$latest_backup/uploads" ]]; then
        log "Restoring uploaded files..."
        rm -rf "$DEPLOY_DIR/uploads"
        cp -r "$latest_backup/uploads" "$DEPLOY_DIR/" || {
            error "File rollback failed"
        }
    fi
    
    # Restart services
    docker-compose -f "$COMPOSE_FILE" restart
    
    success "Rollback completed"
}

# Main deployment function
main() {
    log "Starting AutoApply.co.nz deployment..."
    
    # Trap errors and perform rollback
    trap 'error "Deployment failed, initiating rollback..."; rollback; send_notification "FAILED" "Deployment failed and rolled back"' ERR
    
    check_permissions
    check_prerequisites
    create_backup
    update_code
    update_environment
    pull_images
    run_migrations
    deploy_application
    health_check
    cleanup
    
    success "Deployment completed successfully!"
    send_notification "SUCCESS" "Deployment completed successfully"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "health-check")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health-check|cleanup}"
        exit 1
        ;;
esac
