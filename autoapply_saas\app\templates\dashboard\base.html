{% extends "base.html" %}

{% block content %}
<div class="dashboard-layout">
    <!-- Sidebar -->
    <nav class="dashboard-sidebar bg-white shadow-sm">
        <div class="sidebar-header p-3 border-bottom">
            <div class="d-flex align-items-center">
                <div class="avatar bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    {{ current_user.first_name[0].upper() }}{{ current_user.last_name[0].upper() }}
                </div>
                <div>
                    <div class="fw-bold">{{ current_user.first_name }} {{ current_user.last_name }}</div>
                    <small class="text-muted">{{ current_user.email }}</small>
                </div>
            </div>
        </div>
        
        <div class="sidebar-menu p-3">
            <ul class="nav nav-pills flex-column">
                <li class="nav-item mb-1">
                    <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}" 
                       href="{{ url_for('dashboard.index') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link {% if request.endpoint == 'dashboard.automation' %}active{% endif %}" 
                       href="{{ url_for('dashboard.automation') }}">
                        <i class="fas fa-robot me-2"></i>Automation
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link {% if request.endpoint == 'dashboard.applications' %}active{% endif %}" 
                       href="{{ url_for('dashboard.applications') }}">
                        <i class="fas fa-paper-plane me-2"></i>Applications
                        {% if current_user.get_applications_today() > 0 %}
                            <span class="badge bg-primary ms-auto">{{ current_user.get_applications_today() }}</span>
                        {% endif %}
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link {% if request.endpoint == 'dashboard.settings' %}active{% endif %}" 
                       href="{{ url_for('dashboard.settings') }}">
                        <i class="fas fa-cog me-2"></i>Settings
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link {% if request.endpoint == 'dashboard.billing' %}active{% endif %}" 
                       href="{{ url_for('dashboard.billing') }}">
                        <i class="fas fa-credit-card me-2"></i>Billing
                        {% if current_user.subscription and current_user.subscription.status == 'past_due' %}
                            <span class="badge bg-warning ms-auto">!</span>
                        {% endif %}
                    </a>
                </li>
            </ul>
            
            <hr class="my-3">
            
            <!-- Subscription Status -->
            <div class="subscription-status">
                {% if current_user.is_trial_active() %}
                    <div class="alert alert-info py-2 px-3 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-gift me-2"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold small">Free Trial</div>
                                <div class="small">
                                    {{ current_app.config.get('FREE_TRIAL_APPLICATIONS', 10) - current_user.trial_applications_used }} apps left
                                </div>
                            </div>
                        </div>
                        <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary btn-sm w-100 mt-2">
                            Upgrade Now
                        </a>
                    </div>
                {% elif current_user.has_active_subscription() %}
                    <div class="alert alert-success py-2 px-3 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-crown me-2"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold small">Premium</div>
                                <div class="small">Unlimited applications</div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-warning py-2 px-3 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div class="flex-grow-1">
                                <div class="fw-bold small">No Active Plan</div>
                                <div class="small">Subscribe to apply</div>
                            </div>
                        </div>
                        <a href="{{ url_for('payments.subscribe') }}" class="btn btn-warning btn-sm w-100 mt-2">
                            Subscribe
                        </a>
                    </div>
                {% endif %}
            </div>
            
            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="row g-2 text-center">
                    <div class="col-6">
                        <div class="bg-light rounded p-2">
                            <div class="fw-bold">{{ current_user.get_applications_today() }}</div>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-2">
                            <div class="fw-bold">{{ current_user.job_applications.count() }}</div>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="dashboard-main">
        <div class="dashboard-header bg-white border-bottom px-4 py-3">
            <div class="d-flex align-items-center justify-content-between">
                <button class="btn btn-link d-lg-none p-0" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="d-flex align-items-center ms-auto">
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link position-relative p-2" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                                3
                            </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <div class="dropdown-header">
                                <strong>Notifications</strong>
                            </div>
                            <div class="dropdown-item">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-check-circle text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="small fw-bold">Application Successful</div>
                                        <div class="small text-muted">Applied to Software Developer at TechCorp</div>
                                        <div class="small text-muted">2 hours ago</div>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-item">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-robot text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <div class="small fw-bold">Automation Completed</div>
                                        <div class="small text-muted">5 applications submitted successfully</div>
                                        <div class="small text-muted">1 day ago</div>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="#">View all notifications</a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link d-flex align-items-center p-0" type="button" data-bs-toggle="dropdown">
                            <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                {{ current_user.first_name[0].upper() }}
                            </div>
                            <i class="fas fa-chevron-down small"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">
                                <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>
                                <div class="small text-muted">{{ current_user.email }}</div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('dashboard.settings') }}">
                                <i class="fas fa-user me-2"></i>Profile Settings
                            </a>
                            <a class="dropdown-item" href="{{ url_for('dashboard.billing') }}">
                                <i class="fas fa-credit-card me-2"></i>Billing
                            </a>
                            <a class="dropdown-item" href="{{ url_for('main.contact') }}">
                                <i class="fas fa-question-circle me-2"></i>Help & Support
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="dashboard-content p-4">
            {% block dashboard_content %}{% endblock %}
        </div>
    </main>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="automationToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-robot text-primary me-2"></i>
            <strong class="me-auto">Automation</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <!-- Toast content will be set by JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    padding-top: 76px; /* Account for fixed navbar */
}

.dashboard-sidebar {
    width: 280px;
    position: fixed;
    top: 76px;
    left: 0;
    height: calc(100vh - 76px);
    overflow-y: auto;
    z-index: 1000;
}

.dashboard-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

.dashboard-content {
    flex: 1;
    background-color: #f8f9fa;
}

@media (max-width: 991.98px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .dashboard-sidebar.show {
        transform: translateX(0);
    }
    
    .dashboard-main {
        margin-left: 0;
    }
}

.nav-pills .nav-link {
    color: #6c757d;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.nav-pills .nav-link:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.nav-pills .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.avatar {
    font-size: 0.875rem;
    font-weight: 600;
}

.feature-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #e9ecef;
    border-radius: 12px;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.card {
    border-radius: 12px;
}

.btn {
    border-radius: 8px;
}

.alert {
    border-radius: 8px;
}

.badge {
    font-size: 0.7rem;
}
</style>
{% endblock %}
