<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block meta_description %}Automate your job applications on Seek.co.nz with AI-powered cover letters and smart form filling. Save time and apply to more jobs.{% endblock %}">
    <meta name="keywords" content="job automation, seek.co.nz, job applications, AI cover letters, job search">
    <meta name="author" content="AutoApply.co.nz">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:title" content="{% block og_title %}{{ title or 'AutoApply.co.nz - Automate Your Job Applications' }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Automate your job applications on Seek.co.nz with AI-powered cover letters and smart form filling.{% endblock %}">
    <meta property="og:image" content="{{ url_for('static', filename='images/og-image.png', _external=True) }}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.url }}">
    <meta property="twitter:title" content="{% block twitter_title %}{{ title or 'AutoApply.co.nz - Automate Your Job Applications' }}{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}Automate your job applications on Seek.co.nz with AI-powered cover letters and smart form filling.{% endblock %}">
    <meta property="twitter:image" content="{{ url_for('static', filename='images/og-image.png', _external=True) }}">
    
    <title>{% block title %}{{ title or 'AutoApply.co.nz' }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('main.index') }}">
                <i class="fas fa-robot me-2"></i>AutoApply.co.nz
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.features') }}">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.pricing') }}">Pricing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.how_it_works') }}">How It Works</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ current_user.first_name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('dashboard.index') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('dashboard.settings') }}">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('dashboard.billing') }}">
                                    <i class="fas fa-credit-card me-2"></i>Billing
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-outline-light ms-2" href="{{ url_for('auth.register') }}">Get Started</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-5 pt-4">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main class="{% if not current_user.is_authenticated %}pt-5{% endif %}">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold">
                        <i class="fas fa-robot me-2"></i>AutoApply.co.nz
                    </h5>
                    <p class="text-muted">
                        Automate your job applications on Seek.co.nz with AI-powered cover letters and smart form filling.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold">Product</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.features') }}" class="text-muted text-decoration-none">Features</a></li>
                        <li><a href="{{ url_for('main.pricing') }}" class="text-muted text-decoration-none">Pricing</a></li>
                        <li><a href="{{ url_for('main.how_it_works') }}" class="text-muted text-decoration-none">How It Works</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold">Company</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.about') }}" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="{{ url_for('main.contact') }}" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.privacy') }}" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="{{ url_for('main.terms') }}" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.contact') }}" class="text-muted text-decoration-none">Help Center</a></li>
                        <li><a href="mailto:<EMAIL>" class="text-muted text-decoration-none">Email Support</a></li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 AutoApply.co.nz. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Made with <i class="fas fa-heart text-danger"></i> in New Zealand</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <!-- Analytics -->
    {% if config.GOOGLE_ANALYTICS_ID %}
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config.GOOGLE_ANALYTICS_ID }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config.GOOGLE_ANALYTICS_ID }}');
    </script>
    {% endif %}
</body>
</html>
