{% extends "base.html" %}

{% block content %}
<div class="admin-layout">
    <!-- Admin Sidebar -->
    <nav class="admin-sidebar bg-dark text-white">
        <div class="sidebar-header p-3 border-bottom border-secondary">
            <div class="d-flex align-items-center">
                <div class="avatar bg-danger text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div>
                    <div class="fw-bold">Admin Panel</div>
                    <small class="text-muted">{{ current_user.first_name }} {{ current_user.last_name }}</small>
                </div>
            </div>
        </div>
        
        <div class="sidebar-menu p-3">
            <ul class="nav nav-pills flex-column">
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.index' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.index') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.users' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.users') }}">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.analytics' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.analytics') }}">
                        <i class="fas fa-chart-bar me-2"></i>Analytics
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.monitoring' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.monitoring') }}">
                        <i class="fas fa-heartbeat me-2"></i>Monitoring
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.logs' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.logs') }}">
                        <i class="fas fa-file-alt me-2"></i>System Logs
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link text-white {% if request.endpoint == 'admin.settings' %}active bg-primary{% endif %}" 
                       href="{{ url_for('admin.settings') }}">
                        <i class="fas fa-cog me-2"></i>Settings
                    </a>
                </li>
            </ul>
            
            <hr class="my-3 border-secondary">
            
            <!-- Quick Stats -->
            <div class="quick-stats">
                <h6 class="text-muted small">Quick Stats</h6>
                <div class="row g-2 text-center">
                    <div class="col-6">
                        <div class="bg-secondary bg-opacity-25 rounded p-2">
                            <div class="fw-bold small">{{ stats.users.active if stats else 0 }}</div>
                            <small class="text-muted">Active Users</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-secondary bg-opacity-25 rounded p-2">
                            <div class="fw-bold small">{{ stats.sessions.running if stats else 0 }}</div>
                            <small class="text-muted">Running</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <hr class="my-3 border-secondary">
            
            <!-- Back to Main Site -->
            <div class="d-grid">
                <a href="{{ url_for('main.index') }}" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-arrow-left me-2"></i>Back to Site
                </a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header bg-white border-bottom px-4 py-3">
            <div class="d-flex align-items-center justify-content-between">
                <button class="btn btn-link d-lg-none p-0" id="adminSidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="d-flex align-items-center ms-auto">
                    <!-- System Status -->
                    <div class="me-3">
                        <span class="badge bg-success" id="systemStatus">
                            <i class="fas fa-circle me-1"></i>System Healthy
                        </span>
                    </div>
                    
                    <!-- Admin User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-link d-flex align-items-center p-0" type="button" data-bs-toggle="dropdown">
                            <div class="avatar bg-danger text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <i class="fas fa-chevron-down small"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">
                                <strong>{{ current_user.first_name }} {{ current_user.last_name }}</strong>
                                <div class="small text-muted">Administrator</div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-user me-2"></i>My Dashboard
                            </a>
                            <a class="dropdown-item" href="{{ url_for('admin.settings') }}">
                                <i class="fas fa-cog me-2"></i>Admin Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-content p-4">
            {% block admin_content %}{% endblock %}
        </div>
    </main>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="adminToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-user-shield text-danger me-2"></i>
            <strong class="me-auto">Admin</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            <!-- Toast content will be set by JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.admin-layout {
    display: flex;
    min-height: 100vh;
    padding-top: 76px; /* Account for fixed navbar */
}

.admin-sidebar {
    width: 280px;
    position: fixed;
    top: 76px;
    left: 0;
    height: calc(100vh - 76px);
    overflow-y: auto;
    z-index: 1000;
}

.admin-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

.admin-content {
    flex: 1;
    background-color: #f8f9fa;
}

@media (max-width: 991.98px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
}

.nav-pills .nav-link {
    color: #adb5bd;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.nav-pills .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.nav-pills .nav-link.active {
    background-color: #0d6efd !important;
    color: white;
}

.avatar {
    font-size: 0.875rem;
    font-weight: 600;
}

.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.btn {
    border-radius: 8px;
}

.alert {
    border-radius: 8px;
}

.badge {
    font-size: 0.7rem;
}

.table {
    border-radius: 8px;
    overflow: hidden;
}

.progress {
    border-radius: 4px;
}

/* Status indicators */
.status-healthy { color: #198754; }
.status-warning { color: #ffc107; }
.status-danger { color: #dc3545; }
.status-info { color: #0dcaf0; }

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block extra_scripts %}
<script>
// Admin panel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar toggle for mobile
    const sidebarToggle = document.getElementById('adminSidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const sidebar = document.querySelector('.admin-sidebar');
            sidebar.classList.toggle('show');
        });
    }
    
    // Auto-hide sidebar on mobile when clicking outside
    document.addEventListener('click', function(e) {
        const sidebar = document.querySelector('.admin-sidebar');
        const toggle = document.getElementById('adminSidebarToggle');
        
        if (window.innerWidth <= 991.98 && 
            sidebar && sidebar.classList.contains('show') &&
            !sidebar.contains(e.target) && 
            !toggle.contains(e.target)) {
            sidebar.classList.remove('show');
        }
    });
    
    // Update system status periodically
    updateSystemStatus();
    setInterval(updateSystemStatus, 30000); // Every 30 seconds
});

async function updateSystemStatus() {
    try {
        const response = await fetch('/admin/api/system/health');
        const health = await response.json();
        
        const statusElement = document.getElementById('systemStatus');
        if (statusElement) {
            let statusClass = 'bg-success';
            let statusText = 'System Healthy';
            
            if (health.status === 'degraded') {
                statusClass = 'bg-warning';
                statusText = 'System Degraded';
            } else if (health.status === 'unhealthy') {
                statusClass = 'bg-danger';
                statusText = 'System Unhealthy';
            }
            
            statusElement.className = `badge ${statusClass}`;
            statusElement.innerHTML = `<i class="fas fa-circle me-1"></i>${statusText}`;
        }
    } catch (error) {
        console.error('Error updating system status:', error);
    }
}

function showAdminToast(message, type = 'info') {
    const toast = document.getElementById('adminToast');
    const toastBody = toast.querySelector('.toast-body');
    
    // Set toast content and style
    toastBody.textContent = message;
    
    // Remove existing type classes
    toast.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info');
    
    // Add appropriate class based on type
    switch (type) {
        case 'success':
            toast.classList.add('bg-success', 'text-white');
            break;
        case 'error':
            toast.classList.add('bg-danger', 'text-white');
            break;
        case 'warning':
            toast.classList.add('bg-warning');
            break;
        default:
            toast.classList.add('bg-info', 'text-white');
    }

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// Handle window resize for responsive sidebar
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.admin-sidebar');
    if (window.innerWidth > 991.98 && sidebar) {
        sidebar.classList.remove('show');
    }
});
</script>
{% endblock %}
