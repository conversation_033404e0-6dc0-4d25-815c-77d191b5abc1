{% extends "base.html" %}

{% block title %}Contact Us - AutoApply.co.nz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/marketing.css') }}">
{% endblock %}

{% block content %}
<!-- Contact Hero -->
<section class="contact-hero py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">Get in Touch</h1>
                <p class="lead text-muted mb-4">
                    Have questions about AutoApply? We're here to help you succeed in your job search.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Options -->
<section class="contact-options py-5">
    <div class="container">
        <div class="row g-4 mb-5">
            <div class="col-lg-4 col-md-6">
                <div class="contact-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-envelope text-primary fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Email Support</h5>
                    <p class="text-muted mb-3">
                        Get help with your account, billing, or technical issues.
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                        <EMAIL>
                    </a>
                    <div class="response-time mt-2">
                        <small class="text-muted">Response within 24 hours</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="contact-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-handshake text-success fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Sales Inquiries</h5>
                    <p class="text-muted mb-3">
                        Interested in enterprise plans or have custom requirements?
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-success">
                        <EMAIL>
                    </a>
                    <div class="response-time mt-2">
                        <small class="text-muted">Response within 4 hours</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="contact-card h-100 p-4 bg-white rounded-3 shadow-sm text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-comments text-warning fa-3x"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Live Chat</h5>
                    <p class="text-muted mb-3">
                        Chat with our support team for immediate assistance.
                    </p>
                    <button class="btn btn-outline-warning" onclick="openLiveChat()">
                        Start Live Chat
                    </button>
                    <div class="response-time mt-2">
                        <small class="text-muted">Available 9 AM - 6 PM NZST</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="contact-form py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container bg-white rounded-3 shadow-sm p-5">
                    <div class="text-center mb-4">
                        <h2 class="h1 fw-bold mb-3">Send us a Message</h2>
                        <p class="text-muted">
                            Fill out the form below and we'll get back to you as soon as possible.
                        </p>
                    </div>
                    
                    <form id="contactForm" method="POST">
                        {{ csrf_token() }}
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="firstName" name="first_name" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="lastName" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">Choose a subject...</option>
                                <option value="general">General Inquiry</option>
                                <option value="support">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="feature">Feature Request</option>
                                <option value="partnership">Partnership Opportunity</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" 
                                      placeholder="Tell us how we can help you..." required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for job search tips and product updates
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg py-3">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="contact-faq py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="h1 fw-bold mb-4">Frequently Asked Questions</h2>
                <p class="lead text-muted">
                    Quick answers to common questions
                </p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="contactFaqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                How quickly will I get a response?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body">
                                We aim to respond to all inquiries within 24 hours. Sales inquiries are typically answered within 4 hours during business hours (9 AM - 6 PM NZST).
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Do you offer phone support?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body">
                                Currently, we provide support via email and live chat. Phone support is available for Enterprise customers. We're working on expanding phone support to all users.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                Can I schedule a demo?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body">
                                Yes! Contact our sales <NAME_EMAIL> to schedule a personalized demo. We offer 30-minute demo sessions to show you how AutoApply can benefit your job search.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                What information should I include in my message?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body">
                                Please include as much detail as possible about your question or issue. For technical problems, include your account email and a description of what you were trying to do when the issue occurred.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                Do you have a knowledge base?
                            </button>
                        </h2>
                        <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#contactFaqAccordion">
                            <div class="accordion-body">
                                Yes! Visit our <a href="{{ url_for('main.help') }}">Help Center</a> for detailed guides, tutorials, and troubleshooting articles. Many common questions can be answered there instantly.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Office Information -->
<section class="office-info py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="office-card bg-white rounded-3 shadow-sm p-5 text-center">
                    <h3 class="fw-bold mb-4">Our Office</h3>
                    
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="office-detail">
                                <h5 class="fw-bold mb-2">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    Address
                                </h5>
                                <p class="text-muted">
                                    Level 5, 123 Queen Street<br>
                                    Auckland 1010<br>
                                    New Zealand
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="office-detail">
                                <h5 class="fw-bold mb-2">
                                    <i class="fas fa-clock text-success me-2"></i>
                                    Business Hours
                                </h5>
                                <p class="text-muted">
                                    Monday - Friday: 9:00 AM - 6:00 PM<br>
                                    Saturday: 10:00 AM - 2:00 PM<br>
                                    Sunday: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <p class="text-muted mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            All times are in New Zealand Standard Time (NZST)
                        </p>
                        
                        <div class="social-links">
                            <a href="#" class="btn btn-outline-primary me-2">
                                <i class="fab fa-linkedin me-1"></i>LinkedIn
                            </a>
                            <a href="#" class="btn btn-outline-info me-2">
                                <i class="fab fa-twitter me-1"></i>Twitter
                            </a>
                            <a href="#" class="btn btn-outline-dark">
                                <i class="fab fa-facebook me-1"></i>Facebook
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
// Contact form handling
document.getElementById('contactForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;
    
    try {
        const formData = new FormData(this);
        const response = await fetch(this.action || window.location.pathname, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show success message
            showAlert('success', 'Message sent successfully! We\'ll get back to you soon.');
            this.reset();
        } else {
            showAlert('error', result.error || 'Failed to send message. Please try again.');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        showAlert('error', 'Network error. Please check your connection and try again.');
    } finally {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Live chat function
function openLiveChat() {
    // In a real implementation, this would open your live chat widget
    // For now, we'll show a placeholder message
    showAlert('info', 'Live chat is coming soon! Please use email support for now.');
}

// Alert function
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Form validation
document.querySelectorAll('#contactForm input, #contactForm select, #contactForm textarea').forEach(field => {
    field.addEventListener('blur', function() {
        validateField(this);
    });
});

function validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    
    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');
    
    if (isRequired && !value) {
        field.classList.add('is-invalid');
        return false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            field.classList.add('is-invalid');
            return false;
        }
    }
    
    if (value) {
        field.classList.add('is-valid');
    }
    
    return true;
}

// Track contact interactions
document.querySelectorAll('.contact-card a, .contact-card button').forEach(element => {
    element.addEventListener('click', function() {
        const contactMethod = this.closest('.contact-card').querySelector('h5').textContent;
        
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                'event_category': 'Contact',
                'event_label': contactMethod,
                'value': 1
            });
        }
    });
});
</script>
{% endblock %}
