{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">System Monitoring</h1>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshMonitoring()">
                    <i class="fas fa-sync me-2"></i>Refresh
                </button>
                <button class="btn btn-outline-success" onclick="runHealthCheck()">
                    <i class="fas fa-stethoscope me-2"></i>Health Check
                </button>
            </div>
        </div>
    </div>
</div>

<!-- System Health Overview -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    {% if system_health.status == 'healthy' %}
                        <i class="fas fa-check-circle text-success fa-3x"></i>
                    {% elif system_health.status == 'degraded' %}
                        <i class="fas fa-exclamation-triangle text-warning fa-3x"></i>
                    {% else %}
                        <i class="fas fa-times-circle text-danger fa-3x"></i>
                    {% endif %}
                </div>
                <h5 class="fw-bold">System Status</h5>
                <p class="mb-0 text-capitalize">{{ system_health.status or 'Unknown' }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-database text-primary fa-3x"></i>
                </div>
                <h5 class="fw-bold">Database</h5>
                <p class="mb-0">
                    {% if system_health.checks.database.status == 'healthy' %}
                        <span class="text-success">Connected</span>
                    {% else %}
                        <span class="text-danger">Error</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-memory text-info fa-3x"></i>
                </div>
                <h5 class="fw-bold">Redis</h5>
                <p class="mb-0">
                    {% if system_health.checks.redis.status == 'healthy' %}
                        <span class="text-success">Connected</span>
                    {% else %}
                        <span class="text-danger">Error</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-cogs text-warning fa-3x"></i>
                </div>
                <h5 class="fw-bold">Celery Workers</h5>
                <p class="mb-0">
                    {% if system_health.checks.celery.status == 'healthy' %}
                        <span class="text-success">Active</span>
                    {% else %}
                        <span class="text-danger">Offline</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Queue Status -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Automation Queue Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 mb-0 text-primary">{{ queue_status.active_sessions or 0 }}</div>
                            <small class="text-muted">Active Sessions</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 mb-0 text-info">{{ queue_status.queue_length or 0 }}</div>
                            <small class="text-muted">Queued Jobs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 mb-0 text-success">{{ queue_status.available_slots or 0 }}</div>
                            <small class="text-muted">Available Slots</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 mb-0 text-warning">{{ queue_status.success_rate_recent|round(1) or 0 }}%</div>
                            <small class="text-muted">Success Rate</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Queue Capacity</h6>
                        <div class="progress mb-2" style="height: 8px;">
                            {% set capacity_percent = (queue_status.active_sessions / queue_status.max_concurrent_sessions * 100) if queue_status.max_concurrent_sessions else 0 %}
                            <div class="progress-bar" role="progressbar" style="width: {{ capacity_percent }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted">
                            <span>{{ queue_status.active_sessions or 0 }} / {{ queue_status.max_concurrent_sessions or 5 }}</span>
                            <span>{{ capacity_percent|round }}% utilized</span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">System Health</h6>
                        {% if queue_status.system_healthy %}
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Healthy
                            </span>
                        {% else %}
                            <span class="badge bg-danger">
                                <i class="fas fa-times me-1"></i>Unhealthy
                            </span>
                        {% endif %}
                        
                        <div class="mt-2">
                            <small class="text-muted">
                                Recent: {{ queue_status.recent_completed or 0 }} completed, 
                                {{ queue_status.recent_failed or 0 }} failed
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>System Resources
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Disk Space</span>
                        {% if system_health.checks.disk %}
                            {% if system_health.checks.disk.status == 'healthy' %}
                                <span class="text-success">{{ system_health.checks.disk.message }}</span>
                            {% elif system_health.checks.disk.status == 'warning' %}
                                <span class="text-warning">{{ system_health.checks.disk.message }}</span>
                            {% else %}
                                <span class="text-danger">{{ system_health.checks.disk.message }}</span>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">Unknown</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Database Connections</span>
                        <span class="text-success">Active</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Cache Status</span>
                        {% if system_health.checks.redis.status == 'healthy' %}
                            <span class="text-success">Connected</span>
                        {% else %}
                            <span class="text-danger">Disconnected</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Background Workers</span>
                        {% if system_health.checks.celery.status == 'healthy' %}
                            <span class="text-success">{{ system_health.checks.celery.message }}</span>
                        {% else %}
                            <span class="text-danger">{{ system_health.checks.celery.message }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Active Sessions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-play me-2"></i>Active Automation Sessions
                </h5>
            </div>
            <div class="card-body">
                {% if active_sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Session ID</th>
                                    <th>User</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Progress</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in active_sessions %}
                                <tr>
                                    <td>
                                        <span class="fw-bold">#{{ session.id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 0.75rem;">
                                                {{ session.user.first_name[0].upper() }}{{ session.user.last_name[0].upper() }}
                                            </div>
                                            <div>
                                                <div class="fw-bold small">{{ session.user.first_name }} {{ session.user.last_name }}</div>
                                                <small class="text-muted">{{ session.user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ session.started_at.strftime('%H:%M:%S') }}</div>
                                        <small class="text-muted">{{ session.started_at.strftime('%b %d') }}</small>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ session.duration_minutes() }}</span> min
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <div>
                                                <div class="small fw-bold">{{ session.applications_submitted or 0 }} applications</div>
                                                <small class="text-muted">{{ session.jobs_found or 0 }} jobs found</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="stopSession({{ session.id }})">
                                            <i class="fas fa-stop me-1"></i>Stop
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-pause-circle text-muted fa-3x mb-3"></i>
                        <h5 class="text-muted">No Active Sessions</h5>
                        <p class="text-muted">All automation sessions are currently idle.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Errors -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Recent Errors (Last 24 Hours)
                    </h5>
                    <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-primary btn-sm">
                        View All Logs
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_errors %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Time</th>
                                    <th>Level</th>
                                    <th>Message</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for error in recent_errors %}
                                <tr>
                                    <td>
                                        <div>{{ error.created_at.strftime('%H:%M:%S') }}</div>
                                        <small class="text-muted">{{ error.created_at.strftime('%b %d') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ error.level }}</span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 400px;" title="{{ error.message }}">
                                            {{ error.message }}
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ error.source or 'System' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h5 class="text-success">No Recent Errors</h5>
                        <p class="text-muted">System is running smoothly with no errors in the last 24 hours.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Auto-refresh monitoring data every 30 seconds
setInterval(refreshMonitoring, 30000);

function refreshMonitoring() {
    // Add loading state
    document.body.classList.add('loading');
    
    // Reload the page
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

async function runHealthCheck() {
    try {
        showAdminToast('Running health check...', 'info');
        
        const response = await fetch('/admin/api/system/health');
        const health = await response.json();
        
        if (health.status === 'healthy') {
            showAdminToast('System health check passed!', 'success');
        } else {
            showAdminToast(`System health check failed: ${health.status}`, 'warning');
        }
        
        // Refresh the page to show updated status
        setTimeout(() => {
            window.location.reload();
        }, 2000);
        
    } catch (error) {
        console.error('Error running health check:', error);
        showAdminToast('Health check failed', 'error');
    }
}

function stopSession(sessionId) {
    if (!confirm('Are you sure you want to stop this automation session?')) {
        return;
    }
    
    // Implementation for stopping session
    showAdminToast('Session stop feature coming soon!', 'info');
}
</script>
{% endblock %}
