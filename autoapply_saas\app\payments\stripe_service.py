"""
Stripe service for AutoApply.co.nz subscription management
"""

import stripe
from datetime import datetime
from flask import current_app
from app import db
from app.models import User, Subscription

class StripeService:
    """Service class for Stripe payment processing"""
    
    def __init__(self):
        self.stripe = stripe
        self._configure_stripe()
    
    def _configure_stripe(self):
        """Configure Stripe with API keys"""
        self.stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
        if not self.stripe.api_key:
            current_app.logger.warning("Stripe secret key not configured")
    
    def create_customer(self, user):
        """
        Create a Stripe customer for the user
        
        Args:
            user: User model instance
            
        Returns:
            str: Stripe customer ID
        """
        try:
            customer = self.stripe.Customer.create(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
                metadata={
                    'user_id': user.id,
                    'platform': 'autoapply_saas'
                }
            )
            
            current_app.logger.info(f"Created Stripe customer {customer.id} for user {user.id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Failed to create Stripe customer for user {user.id}: {e}")
            raise
    
    def create_checkout_session(self, user, success_url, cancel_url):
        """
        Create a Stripe Checkout session for subscription
        
        Args:
            user: User model instance
            success_url: URL to redirect after successful payment
            cancel_url: URL to redirect after cancelled payment
            
        Returns:
            dict: Checkout session data
        """
        try:
            # Get or create Stripe customer
            if user.subscription and user.subscription.stripe_customer_id:
                customer_id = user.subscription.stripe_customer_id
            else:
                customer_id = self.create_customer(user)
                
                # Create or update subscription record
                if not user.subscription:
                    subscription = Subscription(
                        user_id=user.id,
                        stripe_customer_id=customer_id,
                        status='incomplete'
                    )
                    db.session.add(subscription)
                else:
                    user.subscription.stripe_customer_id = customer_id
                
                db.session.commit()
            
            # Create checkout session
            session = self.stripe.checkout.Session.create(
                customer=customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': current_app.config.get('STRIPE_MONTHLY_PRICE_ID'),
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': user.id,
                    'platform': 'autoapply_saas'
                },
                subscription_data={
                    'metadata': {
                        'user_id': user.id,
                        'platform': 'autoapply_saas'
                    }
                }
            )
            
            current_app.logger.info(f"Created checkout session {session.id} for user {user.id}")
            return session
            
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Failed to create checkout session for user {user.id}: {e}")
            raise
    
    def create_customer_portal_session(self, user, return_url):
        """
        Create a Stripe Customer Portal session for subscription management
        
        Args:
            user: User model instance
            return_url: URL to return to after portal session
            
        Returns:
            dict: Portal session data
        """
        try:
            if not user.subscription or not user.subscription.stripe_customer_id:
                raise ValueError("User does not have a Stripe customer ID")
            
            session = self.stripe.billing_portal.Session.create(
                customer=user.subscription.stripe_customer_id,
                return_url=return_url,
            )
            
            current_app.logger.info(f"Created portal session for user {user.id}")
            return session
            
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Failed to create portal session for user {user.id}: {e}")
            raise
    
    def get_subscription(self, subscription_id):
        """
        Get subscription details from Stripe
        
        Args:
            subscription_id: Stripe subscription ID
            
        Returns:
            dict: Subscription data
        """
        try:
            subscription = self.stripe.Subscription.retrieve(subscription_id)
            return subscription
            
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Failed to retrieve subscription {subscription_id}: {e}")
            raise
    
    def cancel_subscription(self, subscription_id, at_period_end=True):
        """
        Cancel a subscription
        
        Args:
            subscription_id: Stripe subscription ID
            at_period_end: Whether to cancel at period end or immediately
            
        Returns:
            dict: Updated subscription data
        """
        try:
            if at_period_end:
                subscription = self.stripe.Subscription.modify(
                    subscription_id,
                    cancel_at_period_end=True
                )
            else:
                subscription = self.stripe.Subscription.delete(subscription_id)
            
            current_app.logger.info(f"Cancelled subscription {subscription_id}")
            return subscription
            
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Failed to cancel subscription {subscription_id}: {e}")
            raise
    
    def handle_webhook_event(self, event):
        """
        Handle Stripe webhook events
        
        Args:
            event: Stripe event object
            
        Returns:
            bool: True if handled successfully
        """
        try:
            event_type = event['type']
            current_app.logger.info(f"Handling Stripe webhook event: {event_type}")
            
            if event_type == 'checkout.session.completed':
                return self._handle_checkout_completed(event['data']['object'])
            
            elif event_type == 'customer.subscription.created':
                return self._handle_subscription_created(event['data']['object'])
            
            elif event_type == 'customer.subscription.updated':
                return self._handle_subscription_updated(event['data']['object'])
            
            elif event_type == 'customer.subscription.deleted':
                return self._handle_subscription_deleted(event['data']['object'])
            
            elif event_type == 'invoice.payment_succeeded':
                return self._handle_payment_succeeded(event['data']['object'])
            
            elif event_type == 'invoice.payment_failed':
                return self._handle_payment_failed(event['data']['object'])
            
            else:
                current_app.logger.info(f"Unhandled webhook event type: {event_type}")
                return True
                
        except Exception as e:
            current_app.logger.error(f"Error handling webhook event {event_type}: {e}")
            return False
    
    def _handle_checkout_completed(self, session):
        """Handle successful checkout completion"""
        try:
            user_id = session['metadata'].get('user_id')
            if not user_id:
                current_app.logger.error("No user_id in checkout session metadata")
                return False
            
            user = User.query.get(int(user_id))
            if not user:
                current_app.logger.error(f"User {user_id} not found for checkout session")
                return False
            
            # Update subscription with Stripe subscription ID
            if session['mode'] == 'subscription':
                stripe_subscription_id = session['subscription']
                
                if user.subscription:
                    user.subscription.stripe_subscription_id = stripe_subscription_id
                    user.subscription.status = 'active'
                else:
                    subscription = Subscription(
                        user_id=user.id,
                        stripe_customer_id=session['customer'],
                        stripe_subscription_id=stripe_subscription_id,
                        status='active'
                    )
                    db.session.add(subscription)
                
                db.session.commit()
                current_app.logger.info(f"Updated subscription for user {user_id}")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling checkout completion: {e}")
            return False
    
    def _handle_subscription_created(self, subscription):
        """Handle subscription creation"""
        try:
            user_id = subscription['metadata'].get('user_id')
            if not user_id:
                current_app.logger.warning("No user_id in subscription metadata")
                return True
            
            user = User.query.get(int(user_id))
            if not user:
                current_app.logger.error(f"User {user_id} not found for subscription")
                return False
            
            # Update subscription details
            if user.subscription:
                user.subscription.stripe_subscription_id = subscription['id']
                user.subscription.status = subscription['status']
                user.subscription.current_period_start = datetime.fromtimestamp(subscription['current_period_start'])
                user.subscription.current_period_end = datetime.fromtimestamp(subscription['current_period_end'])
            
            db.session.commit()
            current_app.logger.info(f"Created subscription for user {user_id}")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling subscription creation: {e}")
            return False
    
    def _handle_subscription_updated(self, subscription):
        """Handle subscription updates"""
        try:
            # Find subscription by Stripe ID
            local_subscription = Subscription.query.filter_by(
                stripe_subscription_id=subscription['id']
            ).first()
            
            if not local_subscription:
                current_app.logger.warning(f"Local subscription not found for Stripe ID {subscription['id']}")
                return True
            
            # Update subscription details
            local_subscription.status = subscription['status']
            local_subscription.current_period_start = datetime.fromtimestamp(subscription['current_period_start'])
            local_subscription.current_period_end = datetime.fromtimestamp(subscription['current_period_end'])
            local_subscription.cancel_at_period_end = subscription.get('cancel_at_period_end', False)
            
            if subscription['status'] == 'canceled':
                local_subscription.canceled_at = datetime.utcnow()
            
            db.session.commit()
            current_app.logger.info(f"Updated subscription {local_subscription.id}")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling subscription update: {e}")
            return False
    
    def _handle_subscription_deleted(self, subscription):
        """Handle subscription deletion"""
        try:
            # Find subscription by Stripe ID
            local_subscription = Subscription.query.filter_by(
                stripe_subscription_id=subscription['id']
            ).first()
            
            if not local_subscription:
                current_app.logger.warning(f"Local subscription not found for Stripe ID {subscription['id']}")
                return True
            
            # Update subscription status
            local_subscription.status = 'canceled'
            local_subscription.canceled_at = datetime.utcnow()
            
            db.session.commit()
            current_app.logger.info(f"Deleted subscription {local_subscription.id}")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling subscription deletion: {e}")
            return False
    
    def _handle_payment_succeeded(self, invoice):
        """Handle successful payment"""
        try:
            subscription_id = invoice.get('subscription')
            if not subscription_id:
                return True
            
            # Find subscription by Stripe ID
            local_subscription = Subscription.query.filter_by(
                stripe_subscription_id=subscription_id
            ).first()
            
            if local_subscription:
                # Update subscription status if it was past due
                if local_subscription.status == 'past_due':
                    local_subscription.status = 'active'
                    db.session.commit()
                    current_app.logger.info(f"Reactivated subscription {local_subscription.id} after payment")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling payment success: {e}")
            return False
    
    def _handle_payment_failed(self, invoice):
        """Handle failed payment"""
        try:
            subscription_id = invoice.get('subscription')
            if not subscription_id:
                return True
            
            # Find subscription by Stripe ID
            local_subscription = Subscription.query.filter_by(
                stripe_subscription_id=subscription_id
            ).first()
            
            if local_subscription:
                # Update subscription status
                local_subscription.status = 'past_due'
                db.session.commit()
                current_app.logger.info(f"Marked subscription {local_subscription.id} as past due")
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error handling payment failure: {e}")
            return False
