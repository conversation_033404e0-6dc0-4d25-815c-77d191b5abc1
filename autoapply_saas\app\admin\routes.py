"""
Admin routes for AutoApply.co.nz
Administrative interface and system management
"""

from datetime import datetime, timedelta
from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from functools import wraps
from sqlalchemy import func, desc
from app import db
from app.admin import bp
from app.models import User, JobApplication, AutomationSession, Subscription, SystemLog

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.email.endswith('@seek.co.nz'):
            flash('Admin access required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """Admin dashboard overview"""
    # Get system statistics
    stats = get_system_statistics()
    
    # Get recent activity
    recent_users = User.query.order_by(desc(User.created_at)).limit(5).all()
    recent_sessions = AutomationSession.query.order_by(desc(AutomationSession.started_at)).limit(10).all()
    recent_logs = SystemLog.query.order_by(desc(SystemLog.created_at)).limit(20).all()
    
    return render_template('admin/index.html',
                         title='Admin Dashboard - AutoApply.co.nz',
                         stats=stats,
                         recent_users=recent_users,
                         recent_sessions=recent_sessions,
                         recent_logs=recent_logs)

@bp.route('/users')
@login_required
@admin_required
def users():
    """User management page"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Build query with filters
    query = User.query
    
    # Search filter
    search = request.args.get('search', '')
    if search:
        query = query.filter(
            db.or_(
                User.email.contains(search),
                User.first_name.contains(search),
                User.last_name.contains(search)
            )
        )
    
    # Status filter
    status = request.args.get('status', '')
    if status == 'active':
        query = query.filter(User.is_active == True)
    elif status == 'inactive':
        query = query.filter(User.is_active == False)
    elif status == 'verified':
        query = query.filter(User.is_verified == True)
    elif status == 'unverified':
        query = query.filter(User.is_verified == False)
    
    # Subscription filter
    subscription_status = request.args.get('subscription', '')
    if subscription_status == 'trial':
        query = query.filter(User.trial_started_at.isnot(None))
    elif subscription_status == 'premium':
        query = query.join(Subscription).filter(Subscription.status == 'active')
    elif subscription_status == 'expired':
        query = query.join(Subscription).filter(Subscription.status.in_(['canceled', 'past_due']))
    
    users = query.order_by(desc(User.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('admin/users.html',
                         title='User Management - Admin',
                         users=users,
                         search=search,
                         status=status,
                         subscription_status=subscription_status)

@bp.route('/users/<int:user_id>')
@login_required
@admin_required
def user_detail(user_id):
    """User detail page"""
    user = User.query.get_or_404(user_id)
    
    # Get user statistics
    user_stats = {
        'total_applications': user.job_applications.count(),
        'applications_today': user.get_applications_today(),
        'total_sessions': user.automation_sessions.count(),
        'completed_sessions': user.automation_sessions.filter_by(status='completed').count(),
        'failed_sessions': user.automation_sessions.filter_by(status='failed').count(),
        'trial_applications_used': user.trial_applications_used,
        'last_login': user.last_login_at,
        'account_age_days': (datetime.utcnow() - user.created_at).days
    }
    
    # Get recent activity
    recent_applications = user.job_applications.order_by(desc(JobApplication.applied_at)).limit(10).all()
    recent_sessions = user.automation_sessions.order_by(desc(AutomationSession.started_at)).limit(5).all()
    
    return render_template('admin/user_detail.html',
                         title=f'User: {user.email} - Admin',
                         user=user,
                         user_stats=user_stats,
                         recent_applications=recent_applications,
                         recent_sessions=recent_sessions)

@bp.route('/analytics')
@login_required
@admin_required
def analytics():
    """Analytics and reporting page"""
    # Get date range from query params
    days = request.args.get('days', 30, type=int)
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # User analytics
    user_analytics = get_user_analytics(start_date)
    
    # Application analytics
    application_analytics = get_application_analytics(start_date)
    
    # Revenue analytics
    revenue_analytics = get_revenue_analytics(start_date)
    
    # System performance
    system_performance = get_system_performance(start_date)
    
    return render_template('admin/analytics.html',
                         title='Analytics - Admin',
                         days=days,
                         user_analytics=user_analytics,
                         application_analytics=application_analytics,
                         revenue_analytics=revenue_analytics,
                         system_performance=system_performance)

@bp.route('/monitoring')
@login_required
@admin_required
def monitoring():
    """System monitoring page"""
    # Get system health
    system_health = get_system_health()
    
    # Get active sessions
    active_sessions = AutomationSession.query.filter_by(status='running').all()
    
    # Get recent errors
    recent_errors = SystemLog.query.filter(
        SystemLog.level == 'ERROR',
        SystemLog.created_at >= datetime.utcnow() - timedelta(hours=24)
    ).order_by(desc(SystemLog.created_at)).limit(50).all()
    
    # Get queue status
    from app.automation.queue_manager import QueueManager
    queue_manager = QueueManager()
    queue_status = queue_manager.get_queue_status()
    
    return render_template('admin/monitoring.html',
                         title='System Monitoring - Admin',
                         system_health=system_health,
                         active_sessions=active_sessions,
                         recent_errors=recent_errors,
                         queue_status=queue_status)

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """System logs page"""
    page = request.args.get('page', 1, type=int)
    per_page = 50
    
    # Build query with filters
    query = SystemLog.query
    
    # Level filter
    level = request.args.get('level', '')
    if level:
        query = query.filter(SystemLog.level == level.upper())
    
    # Date filter
    date_filter = request.args.get('date', '')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(func.date(SystemLog.created_at) == filter_date)
        except ValueError:
            pass
    
    # Search filter
    search = request.args.get('search', '')
    if search:
        query = query.filter(SystemLog.message.contains(search))
    
    logs = query.order_by(desc(SystemLog.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('admin/logs.html',
                         title='System Logs - Admin',
                         logs=logs,
                         level=level,
                         date_filter=date_filter,
                         search=search)

@bp.route('/settings')
@login_required
@admin_required
def settings():
    """Admin settings page"""
    return render_template('admin/settings.html',
                         title='Admin Settings - AutoApply.co.nz')

# API Routes for admin actions

@bp.route('/api/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        user = User.query.get_or_404(user_id)
        user.is_active = not user.is_active
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # Log the action
        SystemLog.log_admin_action(
            admin_user_id=current_user.id,
            action=f"{'Activated' if user.is_active else 'Deactivated'} user {user.email}",
            target_user_id=user_id
        )
        
        return jsonify({
            'success': True,
            'message': f"User {'activated' if user.is_active else 'deactivated'} successfully",
            'is_active': user.is_active
        })
        
    except Exception as e:
        current_app.logger.error(f"Error toggling user status: {e}")
        return jsonify({'success': False, 'error': 'Failed to update user status'}), 500

@bp.route('/api/users/<int:user_id>/reset-trial', methods=['POST'])
@login_required
@admin_required
def reset_user_trial(user_id):
    """Reset user trial"""
    try:
        user = User.query.get_or_404(user_id)
        user.trial_started_at = datetime.utcnow()
        user.trial_applications_used = 0
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # Log the action
        SystemLog.log_admin_action(
            admin_user_id=current_user.id,
            action=f"Reset trial for user {user.email}",
            target_user_id=user_id
        )
        
        return jsonify({
            'success': True,
            'message': 'User trial reset successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error resetting user trial: {e}")
        return jsonify({'success': False, 'error': 'Failed to reset trial'}), 500

@bp.route('/api/system/health')
@login_required
@admin_required
def system_health_api():
    """Get system health status"""
    try:
        health = get_system_health()
        return jsonify(health)
    except Exception as e:
        current_app.logger.error(f"Error getting system health: {e}")
        return jsonify({'error': 'Failed to get system health'}), 500

def get_system_statistics():
    """Get comprehensive system statistics"""
    try:
        # User statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        verified_users = User.query.filter_by(is_verified=True).count()
        trial_users = User.query.filter(User.trial_started_at.isnot(None)).count()
        
        # Subscription statistics
        active_subscriptions = Subscription.query.filter_by(status='active').count()
        total_revenue = active_subscriptions * 10  # $10 per subscription
        
        # Application statistics
        total_applications = JobApplication.query.count()
        applications_today = JobApplication.query.filter(
            func.date(JobApplication.applied_at) == datetime.utcnow().date()
        ).count()
        
        # Session statistics
        total_sessions = AutomationSession.query.count()
        running_sessions = AutomationSession.query.filter_by(status='running').count()
        completed_sessions = AutomationSession.query.filter_by(status='completed').count()
        
        # Recent activity (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        new_users_24h = User.query.filter(User.created_at >= yesterday).count()
        applications_24h = JobApplication.query.filter(JobApplication.applied_at >= yesterday).count()
        
        return {
            'users': {
                'total': total_users,
                'active': active_users,
                'verified': verified_users,
                'trial': trial_users,
                'new_24h': new_users_24h
            },
            'subscriptions': {
                'active': active_subscriptions,
                'revenue_monthly': total_revenue
            },
            'applications': {
                'total': total_applications,
                'today': applications_today,
                'last_24h': applications_24h
            },
            'sessions': {
                'total': total_sessions,
                'running': running_sessions,
                'completed': completed_sessions,
                'success_rate': (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
            }
        }
        
    except Exception as e:
        current_app.logger.error(f"Error getting system statistics: {e}")
        return {}

def get_user_analytics(start_date):
    """Get user analytics for the specified period"""
    try:
        # User registrations over time
        registrations = db.session.query(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).filter(
            User.created_at >= start_date
        ).group_by(func.date(User.created_at)).all()

        # User activity
        active_users = User.query.filter(
            User.last_login_at >= start_date,
            User.is_active == True
        ).count()

        # Trial conversions
        trial_users = User.query.filter(User.trial_started_at >= start_date).count()
        converted_users = User.query.join(Subscription).filter(
            User.trial_started_at >= start_date,
            Subscription.status == 'active'
        ).count()

        conversion_rate = (converted_users / trial_users * 100) if trial_users > 0 else 0

        return {
            'registrations': [{'date': r.date.isoformat(), 'count': r.count} for r in registrations],
            'active_users': active_users,
            'trial_users': trial_users,
            'converted_users': converted_users,
            'conversion_rate': round(conversion_rate, 2)
        }

    except Exception as e:
        current_app.logger.error(f"Error getting user analytics: {e}")
        return {}

def get_application_analytics(start_date):
    """Get application analytics for the specified period"""
    try:
        # Applications over time
        applications = db.session.query(
            func.date(JobApplication.applied_at).label('date'),
            func.count(JobApplication.id).label('count')
        ).filter(
            JobApplication.applied_at >= start_date
        ).group_by(func.date(JobApplication.applied_at)).all()

        # Success rate
        total_apps = JobApplication.query.filter(JobApplication.applied_at >= start_date).count()
        successful_apps = JobApplication.query.filter(
            JobApplication.applied_at >= start_date,
            JobApplication.application_status == 'submitted'
        ).count()

        success_rate = (successful_apps / total_apps * 100) if total_apps > 0 else 0

        # Top companies
        top_companies = db.session.query(
            JobApplication.company_name,
            func.count(JobApplication.id).label('count')
        ).filter(
            JobApplication.applied_at >= start_date
        ).group_by(JobApplication.company_name).order_by(desc('count')).limit(10).all()

        return {
            'applications': [{'date': a.date.isoformat(), 'count': a.count} for a in applications],
            'total_applications': total_apps,
            'successful_applications': successful_apps,
            'success_rate': round(success_rate, 2),
            'top_companies': [{'name': c.company_name, 'count': c.count} for c in top_companies]
        }

    except Exception as e:
        current_app.logger.error(f"Error getting application analytics: {e}")
        return {}

def get_revenue_analytics(start_date):
    """Get revenue analytics for the specified period"""
    try:
        # Monthly recurring revenue
        active_subs = Subscription.query.filter_by(status='active').count()
        mrr = active_subs * 10  # $10 per subscription

        # New subscriptions
        new_subs = Subscription.query.filter(
            Subscription.created_at >= start_date,
            Subscription.status == 'active'
        ).count()

        # Churn rate
        canceled_subs = Subscription.query.filter(
            Subscription.canceled_at >= start_date
        ).count()

        churn_rate = (canceled_subs / active_subs * 100) if active_subs > 0 else 0

        return {
            'mrr': mrr,
            'new_subscriptions': new_subs,
            'canceled_subscriptions': canceled_subs,
            'churn_rate': round(churn_rate, 2),
            'active_subscriptions': active_subs
        }

    except Exception as e:
        current_app.logger.error(f"Error getting revenue analytics: {e}")
        return {}

def get_system_performance(start_date):
    """Get system performance metrics"""
    try:
        # Session performance
        sessions = AutomationSession.query.filter(AutomationSession.started_at >= start_date).all()

        if sessions:
            avg_duration = sum(s.duration_minutes() for s in sessions) / len(sessions)
            avg_applications = sum(s.applications_submitted for s in sessions if s.applications_submitted) / len(sessions)
        else:
            avg_duration = 0
            avg_applications = 0

        # Error rate
        total_sessions = len(sessions)
        failed_sessions = len([s for s in sessions if s.status == 'failed'])
        error_rate = (failed_sessions / total_sessions * 100) if total_sessions > 0 else 0

        return {
            'avg_session_duration': round(avg_duration, 2),
            'avg_applications_per_session': round(avg_applications, 2),
            'total_sessions': total_sessions,
            'failed_sessions': failed_sessions,
            'error_rate': round(error_rate, 2)
        }

    except Exception as e:
        current_app.logger.error(f"Error getting system performance: {e}")
        return {}

def get_system_health():
    """Get current system health status"""
    try:
        health = {
            'status': 'healthy',
            'checks': {},
            'timestamp': datetime.utcnow().isoformat()
        }

        # Database check
        try:
            db.session.execute('SELECT 1')
            health['checks']['database'] = {'status': 'healthy', 'message': 'Database connection OK'}
        except Exception as e:
            health['checks']['database'] = {'status': 'unhealthy', 'message': str(e)}
            health['status'] = 'unhealthy'

        # Redis check (if available)
        try:
            from app import redis_client
            if redis_client:
                redis_client.ping()
                health['checks']['redis'] = {'status': 'healthy', 'message': 'Redis connection OK'}
        except Exception as e:
            health['checks']['redis'] = {'status': 'unhealthy', 'message': str(e)}
            health['status'] = 'degraded'

        # Celery check
        try:
            from app import celery
            inspect = celery.control.inspect()
            stats = inspect.stats()
            if stats:
                health['checks']['celery'] = {'status': 'healthy', 'message': f'{len(stats)} workers active'}
            else:
                health['checks']['celery'] = {'status': 'unhealthy', 'message': 'No Celery workers found'}
                health['status'] = 'unhealthy'
        except Exception as e:
            health['checks']['celery'] = {'status': 'unhealthy', 'message': str(e)}
            health['status'] = 'unhealthy'

        # Disk space check
        import shutil
        try:
            total, used, free = shutil.disk_usage('/')
            free_percent = (free / total) * 100
            if free_percent > 20:
                health['checks']['disk'] = {'status': 'healthy', 'message': f'{free_percent:.1f}% free space'}
            elif free_percent > 10:
                health['checks']['disk'] = {'status': 'warning', 'message': f'{free_percent:.1f}% free space'}
                if health['status'] == 'healthy':
                    health['status'] = 'degraded'
            else:
                health['checks']['disk'] = {'status': 'critical', 'message': f'{free_percent:.1f}% free space'}
                health['status'] = 'unhealthy'
        except Exception as e:
            health['checks']['disk'] = {'status': 'unknown', 'message': str(e)}

        return health

    except Exception as e:
        current_app.logger.error(f"Error getting system health: {e}")
        return {
            'status': 'unhealthy',
            'checks': {'general': {'status': 'unhealthy', 'message': str(e)}},
            'timestamp': datetime.utcnow().isoformat()
        }
