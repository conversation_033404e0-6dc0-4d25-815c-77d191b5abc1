name: Deploy AutoApply.co.nz

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: autoapply_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8

    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Set up test environment
      run: |
        export DATABASE_URL=postgresql://postgres:postgres@localhost:5432/autoapply_test
        export REDIS_URL=redis://localhost:6379/0
        export SECRET_KEY=test-secret-key
        export FLASK_ENV=testing

    - name: Run tests
      run: |
        export DATABASE_URL=postgresql://postgres:postgres@localhost:5432/autoapply_test
        export REDIS_URL=redis://localhost:6379/0
        export SECRET_KEY=test-secret-key
        export FLASK_ENV=testing
        pytest --cov=app --cov-report=xml --cov-report=html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

    - name: Add server to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy to production server
      run: |
        ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
          cd /opt/autoapply
          
          # Pull latest code
          git pull origin main
          
          # Update environment variables
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" > .env.prod
          echo "SECRET_KEY=${{ secrets.SECRET_KEY }}" >> .env.prod
          echo "STRIPE_PUBLISHABLE_KEY=${{ secrets.STRIPE_PUBLISHABLE_KEY }}" >> .env.prod
          echo "STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }}" >> .env.prod
          echo "STRIPE_WEBHOOK_SECRET=${{ secrets.STRIPE_WEBHOOK_SECRET }}" >> .env.prod
          echo "MAIL_SERVER=${{ secrets.MAIL_SERVER }}" >> .env.prod
          echo "MAIL_PORT=${{ secrets.MAIL_PORT }}" >> .env.prod
          echo "MAIL_USERNAME=${{ secrets.MAIL_USERNAME }}" >> .env.prod
          echo "MAIL_PASSWORD=${{ secrets.MAIL_PASSWORD }}" >> .env.prod
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env.prod
          echo "GRAFANA_PASSWORD=${{ secrets.GRAFANA_PASSWORD }}" >> .env.prod
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env.prod
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env.prod
          echo "S3_BACKUP_BUCKET=${{ secrets.S3_BACKUP_BUCKET }}" >> .env.prod
          
          # Pull latest Docker images
          docker-compose -f docker-compose.prod.yml pull
          
          # Run database migrations
          docker-compose -f docker-compose.prod.yml run --rm web flask db upgrade
          
          # Deploy with zero downtime
          docker-compose -f docker-compose.prod.yml up -d --remove-orphans
          
          # Clean up old images
          docker image prune -f
          
          # Health check
          sleep 30
          curl -f http://localhost/health || exit 1
          
          echo "Deployment completed successfully"
        EOF

    - name: Notify deployment status
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  post-deploy:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Run smoke tests
      run: |
        # Basic health checks
        curl -f https://autoapply.co.nz/health
        curl -f https://autoapply.co.nz/
        
        # Check critical endpoints
        curl -f https://autoapply.co.nz/auth/login
        curl -f https://autoapply.co.nz/pricing
        
        echo "Smoke tests passed"

    - name: Update monitoring dashboards
      run: |
        # Trigger Grafana dashboard refresh
        curl -X POST "https://autoapply.co.nz:3000/api/admin/provisioning/dashboards/reload" \
          -H "Authorization: Bearer ${{ secrets.GRAFANA_API_KEY }}"

    - name: Create deployment record
      run: |
        # Record deployment in monitoring system
        curl -X POST "https://autoapply.co.nz/api/admin/deployments" \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer ${{ secrets.API_KEY }}" \
          -d '{
            "version": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "deployed_at": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
            "deployed_by": "${{ github.actor }}"
          }'
