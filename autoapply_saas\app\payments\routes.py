"""
Payment routes for AutoApply.co.nz
Stripe integration endpoints
"""

import stripe
from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.payments import bp
from app.payments.stripe_service import StripeService
from app.models import Subscription

@bp.route('/subscribe')
@login_required
def subscribe():
    """Subscription page"""
    # Check if user already has active subscription
    if current_user.has_active_subscription():
        flash('You already have an active subscription.', 'info')
        return redirect(url_for('dashboard.billing'))
    
    # Get pricing information
    monthly_price = current_app.config.get('MONTHLY_PRICE_AMOUNT', 1000) / 100  # Convert cents to dollars
    
    return render_template('payments/subscribe.html', 
                         title='Subscribe - AutoApply.co.nz',
                         monthly_price=monthly_price)

@bp.route('/create-checkout-session', methods=['POST'])
@login_required
def create_checkout_session():
    """Create Stripe checkout session"""
    try:
        # Check if user already has active subscription
        if current_user.has_active_subscription():
            return jsonify({'error': 'User already has active subscription'}), 400
        
        stripe_service = StripeService()
        
        # Create checkout session
        success_url = url_for('payments.subscription_success', _external=True)
        cancel_url = url_for('payments.subscribe', _external=True)
        
        session = stripe_service.create_checkout_session(
            user=current_user,
            success_url=success_url,
            cancel_url=cancel_url
        )
        
        return jsonify({'checkout_url': session.url})
        
    except stripe.error.StripeError as e:
        current_app.logger.error(f"Stripe error creating checkout session: {e}")
        return jsonify({'error': 'Payment processing error'}), 500
    
    except Exception as e:
        current_app.logger.error(f"Error creating checkout session: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/subscription-success')
@login_required
def subscription_success():
    """Subscription success page"""
    return render_template('payments/success.html', 
                         title='Subscription Successful - AutoApply.co.nz')

@bp.route('/manage-subscription')
@login_required
def manage_subscription():
    """Redirect to Stripe Customer Portal"""
    try:
        if not current_user.subscription or not current_user.subscription.stripe_customer_id:
            flash('No subscription found to manage.', 'error')
            return redirect(url_for('dashboard.billing'))
        
        stripe_service = StripeService()
        return_url = url_for('dashboard.billing', _external=True)
        
        session = stripe_service.create_customer_portal_session(
            user=current_user,
            return_url=return_url
        )
        
        return redirect(session.url)
        
    except stripe.error.StripeError as e:
        current_app.logger.error(f"Stripe error creating portal session: {e}")
        flash('Unable to access subscription management. Please try again.', 'error')
        return redirect(url_for('dashboard.billing'))
    
    except Exception as e:
        current_app.logger.error(f"Error creating portal session: {e}")
        flash('An error occurred. Please try again.', 'error')
        return redirect(url_for('dashboard.billing'))

@bp.route('/cancel-subscription', methods=['POST'])
@login_required
def cancel_subscription():
    """Cancel user subscription"""
    try:
        if not current_user.subscription or not current_user.subscription.stripe_subscription_id:
            return jsonify({'error': 'No active subscription found'}), 400
        
        stripe_service = StripeService()
        
        # Cancel at period end by default
        at_period_end = request.json.get('at_period_end', True)
        
        stripe_service.cancel_subscription(
            subscription_id=current_user.subscription.stripe_subscription_id,
            at_period_end=at_period_end
        )
        
        # Update local subscription
        current_user.subscription.cancel_at_period_end = at_period_end
        if not at_period_end:
            current_user.subscription.status = 'canceled'
            current_user.subscription.canceled_at = datetime.utcnow()
        
        db.session.commit()
        
        message = 'Subscription will be cancelled at the end of the current period.' if at_period_end else 'Subscription cancelled immediately.'
        
        return jsonify({'success': True, 'message': message})
        
    except stripe.error.StripeError as e:
        current_app.logger.error(f"Stripe error cancelling subscription: {e}")
        return jsonify({'error': 'Payment processing error'}), 500
    
    except Exception as e:
        current_app.logger.error(f"Error cancelling subscription: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/webhook', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhooks"""
    payload = request.get_data(as_text=True)
    sig_header = request.headers.get('Stripe-Signature')
    
    try:
        # Verify webhook signature
        webhook_secret = current_app.config.get('STRIPE_WEBHOOK_SECRET')
        if webhook_secret:
            event = stripe.Webhook.construct_event(
                payload, sig_header, webhook_secret
            )
        else:
            # For development/testing without webhook signature verification
            event = stripe.Event.construct_from(
                request.get_json(), stripe.api_key
            )
        
        # Handle the event
        stripe_service = StripeService()
        success = stripe_service.handle_webhook_event(event)
        
        if success:
            return jsonify({'status': 'success'}), 200
        else:
            return jsonify({'status': 'error'}), 400
            
    except ValueError as e:
        current_app.logger.error(f"Invalid payload in webhook: {e}")
        return jsonify({'error': 'Invalid payload'}), 400
    
    except stripe.error.SignatureVerificationError as e:
        current_app.logger.error(f"Invalid signature in webhook: {e}")
        return jsonify({'error': 'Invalid signature'}), 400
    
    except Exception as e:
        current_app.logger.error(f"Error handling webhook: {e}")
        return jsonify({'error': 'Webhook handling failed'}), 500

@bp.route('/subscription-status')
@login_required
def subscription_status():
    """Get current subscription status"""
    try:
        subscription_data = {
            'has_subscription': current_user.has_active_subscription(),
            'is_trial_active': current_user.is_trial_active(),
            'can_apply': current_user.can_apply_to_jobs()
        }
        
        if current_user.subscription:
            subscription_data.update({
                'status': current_user.subscription.status,
                'current_period_end': current_user.subscription.current_period_end.isoformat() if current_user.subscription.current_period_end else None,
                'cancel_at_period_end': current_user.subscription.cancel_at_period_end,
                'days_remaining': current_user.subscription.days_remaining()
            })
        
        if current_user.trial_started_at:
            subscription_data.update({
                'trial_applications_used': current_user.trial_applications_used,
                'trial_applications_remaining': current_app.config.get('FREE_TRIAL_APPLICATIONS', 10) - current_user.trial_applications_used
            })
        
        return jsonify(subscription_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting subscription status: {e}")
        return jsonify({'error': 'Unable to get subscription status'}), 500

@bp.route('/pricing-info')
def pricing_info():
    """Get pricing information for public display"""
    try:
        pricing_data = {
            'monthly_price': current_app.config.get('MONTHLY_PRICE_AMOUNT', 1000) / 100,
            'currency': 'NZD',
            'free_trial_days': current_app.config.get('FREE_TRIAL_DAYS', 7),
            'free_trial_applications': current_app.config.get('FREE_TRIAL_APPLICATIONS', 10),
            'features': [
                'Unlimited job applications',
                'AI-generated cover letters',
                'Automatic form filling',
                'Rate limit detection',
                'Application tracking',
                'Email notifications'
            ]
        }
        
        return jsonify(pricing_data)
        
    except Exception as e:
        current_app.logger.error(f"Error getting pricing info: {e}")
        return jsonify({'error': 'Unable to get pricing information'}), 500
