{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Automation Control</h1>
            {% if current_user.can_apply_to_jobs() %}
                <button id="startAutomationBtn" class="btn btn-primary">
                    <i class="fas fa-play me-2"></i>Start New Session
                </button>
            {% else %}
                <a href="{{ url_for('payments.subscribe') }}" class="btn btn-outline-primary">
                    <i class="fas fa-crown me-2"></i>Upgrade to Use Automation
                </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Current Session Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>Current Session
                </h5>
            </div>
            <div class="card-body">
                {% if running_session %}
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div>
                                    <div class="fw-bold">Automation Running</div>
                                    <small class="text-muted">
                                        Started {{ running_session.started_at.strftime('%B %d, %Y at %H:%M') }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="progress mb-3" style="height: 8px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%"></div>
                            </div>
                            
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 mb-0 text-primary">{{ running_session.jobs_found or 0 }}</div>
                                        <small class="text-muted">Jobs Found</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 mb-0 text-success">{{ running_session.applications_submitted or 0 }}</div>
                                        <small class="text-muted">Applied</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 mb-0 text-danger">{{ running_session.applications_failed or 0 }}</div>
                                        <small class="text-muted">Failed</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h4 mb-0 text-info">{{ running_session.duration_minutes() }}</div>
                                        <small class="text-muted">Minutes</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <button id="stopAutomationBtn" class="btn btn-outline-danger">
                                <i class="fas fa-stop me-2"></i>Stop Session
                            </button>
                            <div class="mt-3">
                                <small class="text-muted">Session ID: {{ running_session.id }}</small>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-pause-circle text-muted fa-3x mb-3"></i>
                        <h5 class="text-muted">No Active Session</h5>
                        <p class="text-muted mb-4">Start a new automation session to begin applying to jobs automatically.</p>
                        {% if current_user.can_apply_to_jobs() %}
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#automationModal">
                                <i class="fas fa-play me-2"></i>Start Automation
                            </button>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Automation Settings -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Automation Settings
                </h5>
            </div>
            <div class="card-body">
                <form id="automationSettingsForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Job Keywords</label>
                            <input type="text" class="form-control" name="keywords" 
                                   placeholder="e.g., software developer, marketing manager"
                                   value="{{ current_user.user_settings.get_automation_settings().get('keywords', '') if current_user.user_settings else '' }}">
                            <small class="form-text text-muted">Keywords to search for in job titles and descriptions</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Preferred Locations</label>
                            <input type="text" class="form-control" name="locations" 
                                   placeholder="e.g., Auckland, Wellington, Christchurch"
                                   value="{{ current_user.user_settings.get_automation_settings().get('locations', '') if current_user.user_settings else '' }}">
                            <small class="form-text text-muted">Comma-separated list of preferred locations</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Minimum Salary (NZD)</label>
                            <input type="number" class="form-control" name="min_salary" 
                                   placeholder="e.g., 50000"
                                   value="{{ current_user.user_settings.get_automation_settings().get('min_salary', '') if current_user.user_settings else '' }}">
                            <small class="form-text text-muted">Minimum annual salary requirement</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Job Type</label>
                            <select class="form-select" name="job_type">
                                <option value="">Any</option>
                                <option value="full-time" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('job_type') == 'full-time' else '' }}>Full-time</option>
                                <option value="part-time" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('job_type') == 'part-time' else '' }}>Part-time</option>
                                <option value="contract" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('job_type') == 'contract' else '' }}>Contract</option>
                                <option value="casual" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('job_type') == 'casual' else '' }}>Casual</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Automation Preferences</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="generate_cover_letters" id="generateCoverLetters"
                                   {{ 'checked' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('generate_cover_letters', True) else '' }}>
                            <label class="form-check-label" for="generateCoverLetters">
                                Generate AI cover letters for each application
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="skip_applied" id="skipApplied"
                                   {{ 'checked' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('skip_applied', True) else '' }}>
                            <label class="form-check-label" for="skipApplied">
                                Skip jobs I've already applied to
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications"
                                   {{ 'checked' if current_user.user_settings and current_user.user_settings.email_notifications else '' }}>
                            <label class="form-check-label" for="emailNotifications">
                                Send email notifications when sessions complete
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Daily Limits -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gauge me-2"></i>Daily Limits
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Applications Today</span>
                    <span class="fw-bold">{{ current_user.get_applications_today() }}</span>
                </div>
                
                <div class="progress mb-3" style="height: 8px;">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ (current_user.get_applications_today() / 50 * 100) | round }}%"></div>
                </div>
                
                <div class="d-flex justify-content-between text-muted small">
                    <span>0</span>
                    <span>{{ 50 - current_user.get_applications_today() }} remaining</span>
                    <span>50 max</span>
                </div>
                
                {% if current_user.is_trial_active() %}
                    <hr>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Trial Applications</span>
                        <span class="fw-bold">{{ current_user.trial_applications_used }}</span>
                    </div>
                    
                    <div class="progress mb-2" style="height: 6px;">
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ (current_user.trial_applications_used / 10 * 100) | round }}%"></div>
                    </div>
                    
                    <div class="d-flex justify-content-between text-muted small">
                        <span>{{ 10 - current_user.trial_applications_used }} left in trial</span>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="testAutomation()">
                        <i class="fas fa-vial me-2"></i>Test Automation
                    </button>
                    
                    <button class="btn btn-outline-info" onclick="viewQueue()">
                        <i class="fas fa-list me-2"></i>View Queue Status
                    </button>
                    
                    <a href="{{ url_for('dashboard.applications') }}" class="btn btn-outline-success">
                        <i class="fas fa-history me-2"></i>View History
                    </a>
                    
                    <button class="btn btn-outline-warning" onclick="exportSettings()">
                        <i class="fas fa-download me-2"></i>Export Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session History -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Sessions
                    </h5>
                    <a href="{{ url_for('dashboard.applications') }}" class="btn btn-outline-primary btn-sm">
                        View All Applications
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Session</th>
                                    <th>Status</th>
                                    <th>Results</th>
                                    <th>Duration</th>
                                    <th>Started</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in sessions %}
                                <tr>
                                    <td>
                                        <div class="fw-bold">Session #{{ session.id }}</div>
                                        {% if session.error_message %}
                                            <small class="text-danger">{{ session.error_message[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if session.status == 'completed' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Completed
                                            </span>
                                        {% elif session.status == 'failed' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Failed
                                            </span>
                                        {% elif session.status == 'running' %}
                                            <span class="badge bg-primary">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Running
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">
                                                {{ session.status.title() }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="small">
                                            <div><strong>{{ session.applications_submitted }}</strong> applied</div>
                                            {% if session.applications_failed > 0 %}
                                                <div class="text-danger">{{ session.applications_failed }} failed</div>
                                            {% endif %}
                                            {% if session.rate_limited %}
                                                <div class="text-warning">Rate limited</div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ session.duration_minutes() }}</span> min
                                    </td>
                                    <td>
                                        <div>{{ session.started_at.strftime('%b %d, %Y') }}</div>
                                        <small class="text-muted">{{ session.started_at.strftime('%H:%M') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clock text-muted fa-2x mb-3"></i>
                        <h5 class="text-muted">No Sessions Yet</h5>
                        <p class="text-muted">Your automation session history will appear here.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Start Automation Modal -->
<div class="modal fade" id="automationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-robot me-2"></i>Start Job Automation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    The automation will search for jobs on Seek.co.nz and apply to those with Quick Apply buttons using your saved settings.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Current Settings</h6>
                        <ul class="list-unstyled">
                            <li><strong>Keywords:</strong> {{ current_user.user_settings.get_automation_settings().get('keywords', 'Any') if current_user.user_settings else 'Any' }}</li>
                            <li><strong>Locations:</strong> {{ current_user.user_settings.get_automation_settings().get('locations', 'Any') if current_user.user_settings else 'Any' }}</li>
                            <li><strong>AI Cover Letters:</strong> {{ 'Yes' if current_user.user_settings and current_user.user_settings.get_automation_settings().get('generate_cover_letters', True) else 'No' }}</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">Today's Usage</h6>
                        <div class="bg-light rounded p-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold">{{ current_user.get_applications_today() }}</div>
                                    <small class="text-muted">Applied</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold">{{ 50 - current_user.get_applications_today() }}</div>
                                    <small class="text-muted">Remaining</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold">50</div>
                                    <small class="text-muted">Daily Limit</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if current_user.is_trial_active() %}
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-gift me-2"></i>
                    <strong>Free Trial:</strong> {{ 10 - current_user.trial_applications_used }} applications remaining in your trial.
                    <a href="{{ url_for('payments.subscribe') }}" class="alert-link">Upgrade for unlimited applications</a>.
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-outline-primary" onclick="editSettings()">
                    <i class="fas fa-cog me-2"></i>Edit Settings
                </button>
                <button type="button" class="btn btn-primary" id="confirmStartAutomation">
                    <i class="fas fa-play me-2"></i>Start Automation
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Save automation settings
document.getElementById('automationSettingsForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const settings = {
        keywords: formData.get('keywords'),
        locations: formData.get('locations'),
        min_salary: formData.get('min_salary'),
        job_type: formData.get('job_type'),
        generate_cover_letters: formData.get('generate_cover_letters') === 'on',
        skip_applied: formData.get('skip_applied') === 'on',
        email_notifications: formData.get('email_notifications') === 'on'
    };
    
    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ automation_settings: settings })
        });
        
        if (response.ok) {
            showToast('Settings saved successfully!', 'success');
        } else {
            showToast('Failed to save settings', 'error');
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showToast('Network error', 'error');
    }
});

function testAutomation() {
    alert('Test automation feature coming soon!');
}

function viewQueue() {
    alert('Queue status feature coming soon!');
}

function exportSettings() {
    alert('Export settings feature coming soon!');
}

function editSettings() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('automationModal'));
    modal.hide();
    
    // Scroll to settings form
    document.getElementById('automationSettingsForm').scrollIntoView({ behavior: 'smooth' });
}

function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast position-fixed bottom-0 end-0 m-3 bg-${type === 'error' ? 'danger' : type} text-white`;
    toast.innerHTML = `
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
{% endblock %}
