{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">Account Settings</h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Profile Settings -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">First Name</label>
                            <input type="text" class="form-control" name="first_name" 
                                   value="{{ current_user.first_name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Last Name</label>
                            <input type="text" class="form-control" name="last_name" 
                                   value="{{ current_user.last_name }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-control" value="{{ current_user.email }}" disabled>
                        <small class="form-text text-muted">
                            Email cannot be changed. Contact support if you need to update your email.
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-key me-2"></i>Change Password
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Resume Upload -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>Resume/CV
                </h5>
            </div>
            <div class="card-body">
                {% if current_user.user_settings and current_user.user_settings.resume_filename %}
                    <div class="alert alert-success">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Resume Uploaded:</strong> {{ current_user.user_settings.resume_filename }}
                                <br>
                                <small class="text-muted">
                                    Uploaded {{ current_user.user_settings.resume_uploaded_at.strftime('%B %d, %Y at %H:%M') }}
                                </small>
                            </div>
                            <button class="btn btn-outline-primary btn-sm" onclick="downloadResume()">
                                <i class="fas fa-download me-2"></i>Download
                            </button>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>No Resume Uploaded:</strong> Upload your resume to enable AI cover letter generation.
                    </div>
                {% endif %}
                
                <form id="resumeUploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Upload New Resume</label>
                        <input type="file" class="form-control" name="resume" accept=".pdf,.doc,.docx" required>
                        <small class="form-text text-muted">
                            Supported formats: PDF, DOC, DOCX (max 16MB)
                        </small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload Resume
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Job Preferences -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>Job Preferences
                </h5>
            </div>
            <div class="card-body">
                <form id="jobPreferencesForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Preferred Job Titles</label>
                            <textarea class="form-control" name="job_titles" rows="3" 
                                      placeholder="e.g., Software Developer, Full Stack Engineer, Web Developer">{{ current_user.user_settings.get_job_preferences().get('job_titles', '') if current_user.user_settings else '' }}</textarea>
                            <small class="form-text text-muted">One per line or comma-separated</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Preferred Locations</label>
                            <textarea class="form-control" name="locations" rows="3" 
                                      placeholder="e.g., Auckland, Wellington, Christchurch">{{ current_user.user_settings.get_job_preferences().get('locations', '') if current_user.user_settings else '' }}</textarea>
                            <small class="form-text text-muted">One per line or comma-separated</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Minimum Salary (NZD)</label>
                            <input type="number" class="form-control" name="min_salary" 
                                   placeholder="e.g., 50000"
                                   value="{{ current_user.user_settings.get_job_preferences().get('min_salary', '') if current_user.user_settings else '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Maximum Salary (NZD)</label>
                            <input type="number" class="form-control" name="max_salary" 
                                   placeholder="e.g., 120000"
                                   value="{{ current_user.user_settings.get_job_preferences().get('max_salary', '') if current_user.user_settings else '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Employment Type</label>
                            <select class="form-select" name="employment_type">
                                <option value="">Any</option>
                                <option value="full-time" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('employment_type') == 'full-time' else '' }}>Full-time</option>
                                <option value="part-time" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('employment_type') == 'part-time' else '' }}>Part-time</option>
                                <option value="contract" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('employment_type') == 'contract' else '' }}>Contract</option>
                                <option value="casual" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('employment_type') == 'casual' else '' }}>Casual</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Work Arrangement</label>
                            <select class="form-select" name="work_arrangement">
                                <option value="">Any</option>
                                <option value="office" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('work_arrangement') == 'office' else '' }}>Office-based</option>
                                <option value="remote" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('work_arrangement') == 'remote' else '' }}>Remote</option>
                                <option value="hybrid" {{ 'selected' if current_user.user_settings and current_user.user_settings.get_job_preferences().get('work_arrangement') == 'hybrid' else '' }}>Hybrid</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Skills & Keywords</label>
                        <textarea class="form-control" name="skills" rows="3" 
                                  placeholder="e.g., JavaScript, React, Node.js, Python, AWS">{{ current_user.user_settings.get_job_preferences().get('skills', '') if current_user.user_settings else '' }}</textarea>
                        <small class="form-text text-muted">Skills to highlight in applications</small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Preferences
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Notification Settings -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>Notification Preferences
                </h5>
            </div>
            <div class="card-body">
                <form id="notificationForm">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications"
                               {{ 'checked' if current_user.user_settings and current_user.user_settings.email_notifications else '' }}>
                        <label class="form-check-label" for="emailNotifications">
                            <strong>Email Notifications</strong>
                            <br>
                            <small class="text-muted">Receive email updates about automation sessions and applications</small>
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="daily_summary_email" id="dailySummaryEmail"
                               {{ 'checked' if current_user.user_settings and current_user.user_settings.daily_summary_email else '' }}>
                        <label class="form-check-label" for="dailySummaryEmail">
                            <strong>Daily Summary Email</strong>
                            <br>
                            <small class="text-muted">Receive a daily summary of your job applications</small>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Notification Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Account Status -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-circle me-2"></i>Account Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                        {{ current_user.first_name[0].upper() }}{{ current_user.last_name[0].upper() }}
                    </div>
                    <div>
                        <div class="fw-bold">{{ current_user.first_name }} {{ current_user.last_name }}</div>
                        <small class="text-muted">{{ current_user.email }}</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Account Status</span>
                        {% if current_user.is_verified %}
                            <span class="badge bg-success">Verified</span>
                        {% else %}
                            <span class="badge bg-warning">Unverified</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Member Since</span>
                        <span>{{ current_user.created_at.strftime('%b %Y') }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Total Applications</span>
                        <span class="fw-bold">{{ current_user.job_applications.count() }}</span>
                    </div>
                </div>
                
                {% if not current_user.is_verified %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Email Not Verified</strong>
                    <br>
                    <small>Please verify your email to access all features.</small>
                    <br>
                    <a href="{{ url_for('auth.resend_verification') }}" class="btn btn-warning btn-sm mt-2">
                        Resend Verification
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3 text-center">
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">{{ current_user.get_applications_today() }}</div>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light rounded p-3">
                            <div class="h5 mb-0">{{ current_user.automation_sessions.filter_by(status='completed').count() }}</div>
                            <small class="text-muted">Sessions</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Data Export -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download me-2"></i>Data Export
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-3">
                    Download your data for backup or transfer purposes.
                </p>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportApplications()">
                        <i class="fas fa-file-csv me-2"></i>Export Applications
                    </button>
                    
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportSettings()">
                        <i class="fas fa-cog me-2"></i>Export Settings
                    </button>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteAccount()">
                        <i class="fas fa-trash me-2"></i>Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Profile form submission
document.getElementById('profileForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        first_name: formData.get('first_name'),
        last_name: formData.get('last_name')
    };
    
    try {
        const response = await fetch('/api/user/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showToast('Profile updated successfully!', 'success');
        } else {
            showToast('Failed to update profile', 'error');
        }
    } catch (error) {
        console.error('Error updating profile:', error);
        showToast('Network error', 'error');
    }
});

// Resume upload
document.getElementById('resumeUploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    try {
        const response = await fetch('/dashboard/upload-resume', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Resume uploaded successfully!', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(result.error || 'Failed to upload resume', 'error');
        }
    } catch (error) {
        console.error('Error uploading resume:', error);
        showToast('Network error', 'error');
    }
});

// Job preferences form
document.getElementById('jobPreferencesForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const preferences = {
        job_titles: formData.get('job_titles'),
        locations: formData.get('locations'),
        min_salary: formData.get('min_salary'),
        max_salary: formData.get('max_salary'),
        employment_type: formData.get('employment_type'),
        work_arrangement: formData.get('work_arrangement'),
        skills: formData.get('skills')
    };
    
    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ job_preferences: preferences })
        });
        
        if (response.ok) {
            showToast('Job preferences saved successfully!', 'success');
        } else {
            showToast('Failed to save preferences', 'error');
        }
    } catch (error) {
        console.error('Error saving preferences:', error);
        showToast('Network error', 'error');
    }
});

// Notification settings form
document.getElementById('notificationForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const settings = {
        email_notifications: formData.get('email_notifications') === 'on',
        daily_summary_email: formData.get('daily_summary_email') === 'on'
    };
    
    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settings)
        });
        
        if (response.ok) {
            showToast('Notification settings saved!', 'success');
        } else {
            showToast('Failed to save settings', 'error');
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showToast('Network error', 'error');
    }
});

function downloadResume() {
    // Implementation for resume download
    alert('Resume download feature coming soon!');
}

function exportApplications() {
    window.location.href = '/api/applications/export';
}

function exportSettings() {
    alert('Settings export feature coming soon!');
}

function deleteAccount() {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
        alert('Account deletion feature coming soon! Please contact support for now.');
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast position-fixed bottom-0 end-0 m-3 bg-${type === 'error' ? 'danger' : type} text-white`;
    toast.innerHTML = `
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
{% endblock %}
