"""
Cover letter generation service for AutoApply.co.nz
Refactored from original seek_auto_apply.py OpenAI integration
"""

import re
import os
import openai
from flask import current_app
from app.models import User, UserSettings

class CoverLetterGenerator:
    """AI-powered cover letter generation service"""
    
    def __init__(self):
        self.client = None
        self._setup_openai_client()
    
    def _setup_openai_client(self):
        """Setup OpenAI client with API key from config"""
        api_key = current_app.config.get('OPENAI_API_KEY')
        if api_key:
            self.client = openai.OpenAI(api_key=api_key)
        else:
            current_app.logger.warning("OpenAI API key not configured")
    
    def clean_job_description(self, job_description):
        """Clean HTML and format job description text"""
        if not job_description:
            return ""
        
        # Remove HTML tags
        clean_desc = re.sub(r'<[^>]+>', '', job_description)
        
        # Replace HTML entities
        clean_desc = re.sub(r'&nbsp;', ' ', clean_desc)
        clean_desc = re.sub(r'&amp;', '&', clean_desc)
        clean_desc = re.sub(r'&lt;', '<', clean_desc)
        clean_desc = re.sub(r'&gt;', '>', clean_desc)
        
        # Clean up whitespace
        clean_desc = re.sub(r'\s+', ' ', clean_desc).strip()
        
        return clean_desc
    
    def get_user_resume_text(self, user_id):
        """Get resume text for the user"""
        try:
            user = User.query.get(user_id)
            if not user or not user.user_settings:
                return "Resume not available"
            
            # In a real implementation, you would read the resume file
            # For now, return a placeholder
            resume_path = user.user_settings.resume_file_path
            if resume_path and os.path.exists(resume_path):
                # Read PDF resume (implement PDF reading logic)
                return self._read_resume_pdf(resume_path)
            else:
                return "Resume not available"
                
        except Exception as e:
            current_app.logger.error(f"Error getting resume for user {user_id}: {e}")
            return "Resume not available"
    
    def _read_resume_pdf(self, pdf_path):
        """Read text content from PDF resume file"""
        try:
            import PyPDF2
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
                
                return text.strip()
                
        except Exception as e:
            current_app.logger.error(f"Error reading PDF resume: {e}")
            return "Resume not available due to error"
    
    def generate_cover_letter(self, user_id, job_description, job_title=None, company_name=None):
        """
        Generate a tailored cover letter using OpenAI API
        
        Args:
            user_id: User ID for resume lookup
            job_description: HTML job description from the job posting
            job_title: Job title (optional)
            company_name: Company name (optional)
        
        Returns:
            str: Generated cover letter text
        """
        if not self.client:
            current_app.logger.error("OpenAI client not configured")
            return self._get_fallback_cover_letter()
        
        try:
            current_app.logger.info(f"Generating cover letter for user {user_id}")
            
            # Get user's resume text
            resume_text = self.get_user_resume_text(user_id)
            
            # Clean job description
            clean_job_desc = self.clean_job_description(job_description)
            
            # Create the prompt for OpenAI
            prompt = self._create_cover_letter_prompt(
                resume_text, clean_job_desc, job_title, company_name
            )
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system", 
                        "content": "You are a professional career advisor who writes excellent cover letters. You NEVER use placeholder text or square brackets. You write complete, ready-to-submit cover letters."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )
            
            cover_letter_content = response.choices[0].message.content
            if cover_letter_content:
                cover_letter = cover_letter_content.strip()
                current_app.logger.info(f"Successfully generated cover letter ({len(cover_letter)} characters)")
                return cover_letter
            else:
                current_app.logger.error("OpenAI returned empty content")
                return self._get_fallback_cover_letter()
                
        except Exception as e:
            current_app.logger.error(f"Failed to generate cover letter: {e}")
            return self._get_fallback_cover_letter()
    
    def _create_cover_letter_prompt(self, resume_text, job_description, job_title=None, company_name=None):
        """Create the prompt for OpenAI cover letter generation"""
        company_ref = company_name if company_name else "your organization"
        position_ref = job_title if job_title else "this position"
        
        prompt = f"""
Write a professional cover letter for a job application. Use the following information:

RESUME/CV CONTENT:
{resume_text}

JOB DESCRIPTION:
{job_description}

CRITICAL INSTRUCTIONS:
- Write a concise, professional cover letter (maximum 200 words)
- Highlight relevant skills and experience from the resume that match the job requirements
- Show enthusiasm for the role and {company_ref}
- Use a professional but personable tone
- NEVER EVER include ANY placeholder text such as [Your Name], [Company Name], [Position], [Date], [Hiring Manager], [Company Address], etc.
- ABSOLUTELY DO NOT use square brackets [ ] anywhere in the cover letter under any circumstances
- Write the cover letter as if it's ready to submit immediately without any editing needed
- Focus on specific achievements and skills that align with the job
- End with a strong closing statement
- Refer to the company as "{company_ref}" and the position as "{position_ref}"
- Start with "Dear Hiring Manager," and end with "Sincerely," or "Best regards,"

Cover Letter:
"""
        return prompt
    
    def _get_fallback_cover_letter(self):
        """Return a fallback cover letter when AI generation fails"""
        return """Dear Hiring Manager,

I am writing to express my strong interest in this position. Based on my background and experience, I believe I would be a valuable addition to your team.

My professional experience has equipped me with the skills and knowledge necessary to excel in this role. I am particularly drawn to the opportunity to contribute to your organization and would welcome the chance to discuss how my qualifications align with your needs.

I am excited about the possibility of joining your team and contributing to your continued success. Thank you for considering my application. I look forward to hearing from you.

Best regards"""
    
    def generate_textarea_answer(self, user_id, question_text):
        """
        Generate an appropriate answer to a textarea question using OpenAI API
        
        Args:
            user_id: User ID for resume lookup
            question_text: The label text/question from the textarea field
        
        Returns:
            str: Generated answer text
        """
        if not self.client:
            return "Yes, I have relevant experience and skills for this position as outlined in my resume."
        
        try:
            current_app.logger.info(f"Generating textarea answer for user {user_id}: '{question_text}'")
            
            # Get user's resume text
            resume_text = self.get_user_resume_text(user_id)
            
            # Create the prompt for OpenAI
            prompt = self._create_textarea_prompt(resume_text, question_text)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system", 
                        "content": "You are a professional career advisor helping someone complete job application forms based on their resume."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.7
            )
            
            answer_content = response.choices[0].message.content
            if answer_content:
                answer = answer_content.strip()
                current_app.logger.info(f"Generated answer ({len(answer)} characters): {answer[:100]}{'...' if len(answer) > 100 else ''}")
                return answer
            else:
                current_app.logger.error("OpenAI returned empty content for textarea question")
                return "Yes, I have relevant experience and skills for this position as outlined in my resume."
                
        except Exception as e:
            current_app.logger.error(f"Failed to generate textarea answer: {e}")
            return "Yes, I have relevant experience and skills for this position as outlined in my resume."
    
    def _create_textarea_prompt(self, resume_text, question_text):
        """Create the prompt for textarea answer generation"""
        prompt = f"""
You are helping someone fill out a job application form. Based on their resume/CV content, provide a concise, professional answer to the following question.

RESUME/CV CONTENT:
{resume_text}

QUESTION/FIELD LABEL:
{question_text}

CRITICAL INSTRUCTIONS:
- NEVER EVER include ANY placeholder text such as [Your Name], [Company Name], [Position], [Date], [Hiring Manager], [Company Address], etc.
- ABSOLUTELY DO NOT use square brackets [ ] anywhere in the answer under any circumstances
- Provide a brief, relevant answer (maximum 100 words)
- Base your answer on information from the resume
- If the question is about experience, mention specific relevant experience from the resume
- If the question is about skills, highlight matching skills from the resume
- If the question is about availability, motivation, or general questions, provide a professional response
- If the resume doesn't contain relevant information, provide a brief professional response
- Do not include placeholder text or ask for more information
- Keep the tone professional but personable
- Write the answer as if it's ready to submit immediately without any editing needed

Answer:
"""
        return prompt
