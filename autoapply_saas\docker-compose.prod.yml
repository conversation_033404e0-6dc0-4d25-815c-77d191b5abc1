version: '3.8'

services:
  # Main Flask Application
  web:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: autoapply_web
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://autoapply:${DB_PASSWORD}@db:5432/autoapply_prod
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - MAIL_SERVER=${MAIL_SERVER}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    expose:
      - "8000"
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./exports:/app/exports
    networks:
      - autoapply_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker for Background Tasks
  celery_worker:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: autoapply_celery
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://autoapply:${DB_PASSWORD}@db:5432/autoapply_prod
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CHROME_DRIVER_PATH=/usr/local/bin/chromedriver
      - CHROME_BINARY_PATH=/usr/bin/google-chrome
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./exports:/app/exports
      - chrome_data:/app/chrome_data
    networks:
      - autoapply_network
    healthcheck:
      test: ["CMD", "celery", "-A", "app.celery", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Beat for Scheduled Tasks
  celery_beat:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: autoapply_beat
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://autoapply:${DB_PASSWORD}@db:5432/autoapply_prod
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - autoapply_network

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: autoapply_db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=autoapply_prod
      - POSTGRES_USER=autoapply
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - autoapply_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autoapply -d autoapply_prod"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for Caching and Job Queue
  redis:
    image: redis:7-alpine
    container_name: autoapply_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - autoapply_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: autoapply_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-available:/etc/nginx/sites-available:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - web
    networks:
      - autoapply_network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: autoapply_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - autoapply_network

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: autoapply_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - autoapply_network

  # Log Management with Loki
  loki:
    image: grafana/loki:latest
    container_name: autoapply_loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - autoapply_network

  # Log Collection with Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: autoapply_promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log/autoapply:ro
      - /var/log:/var/log/host:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - autoapply_network

  # Backup Service
  backup:
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: autoapply_backup
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://autoapply:${DB_PASSWORD}@db:5432/autoapply_prod
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - S3_BUCKET=${S3_BACKUP_BUCKET}
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
    volumes:
      - ./backups:/backups
      - postgres_data:/var/lib/postgresql/data:ro
    depends_on:
      - db
    networks:
      - autoapply_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  chrome_data:
    driver: local

networks:
  autoapply_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
