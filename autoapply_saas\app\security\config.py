"""
Security configuration for AutoApply.co.nz
Centralized security settings and compliance configuration
"""

import os
from datetime import timedelta

class SecurityConfig:
    """Security configuration settings"""
    
    # Rate Limiting Configuration
    RATE_LIMITS = {
        'login': {'requests': 5, 'window': 300},  # 5 attempts per 5 minutes
        'register': {'requests': 3, 'window': 3600},  # 3 registrations per hour
        'api': {'requests': 100, 'window': 3600},  # 100 API calls per hour
        'automation': {'requests': 10, 'window': 3600},  # 10 automation starts per hour
        'password_reset': {'requests': 3, 'window': 3600},  # 3 password resets per hour
        'data_export': {'requests': 2, 'window': 86400},  # 2 exports per day
        'suspicious': {'requests': 10, 'window': 300},  # 10 suspicious requests per 5 minutes
    }
    
    # IP Blocking Configuration
    IP_BLOCK_THRESHOLDS = {
        'failed_login': 10,  # Block after 10 failed login attempts
        'failed_api': 20,    # Block after 20 failed API calls
        'malicious_request': 5,  # Block after 5 malicious requests
        'suspicious_activity': 15,  # Block after 15 suspicious activities
    }
    
    IP_BLOCK_DURATIONS = {
        'temporary': 3600,    # 1 hour
        'extended': 86400,    # 24 hours
        'permanent': 2592000, # 30 days
    }
    
    # Security Headers Configuration
    SECURITY_HEADERS = {
        'Content-Security-Policy': (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
            "https://cdn.jsdelivr.net https://js.stripe.com https://checkout.stripe.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "connect-src 'self' https://api.stripe.com; "
            "frame-src https://checkout.stripe.com https://js.stripe.com; "
            "object-src 'none'; "
            "base-uri 'self';"
        ),
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
    }
    
    # GDPR Compliance Configuration
    GDPR_CONFIG = {
        'data_retention_days': 2555,  # 7 years
        'export_formats': ['json', 'csv'],
        'anonymization_enabled': True,
        'consent_required': True,
        'privacy_policy_version': '1.0',
        'cookie_consent_required': True,
    }
    
    # Session Security Configuration
    SESSION_CONFIG = {
        'timeout_minutes': 60,  # 1 hour session timeout
        'secure_cookies': True,  # Require HTTPS for cookies
        'httponly_cookies': True,  # Prevent JavaScript access to cookies
        'samesite_cookies': 'Lax',  # CSRF protection
        'regenerate_on_login': True,  # Regenerate session ID on login
    }
    
    # Password Security Configuration
    PASSWORD_CONFIG = {
        'min_length': 8,
        'require_uppercase': True,
        'require_lowercase': True,
        'require_numbers': True,
        'require_special_chars': True,
        'max_age_days': 90,  # Force password change after 90 days
        'history_count': 5,  # Remember last 5 passwords
        'lockout_attempts': 5,  # Lock account after 5 failed attempts
        'lockout_duration': 1800,  # 30 minutes lockout
    }
    
    # API Security Configuration
    API_CONFIG = {
        'require_api_key': True,
        'api_key_length': 32,
        'api_key_expiry_days': 365,
        'request_signing': False,  # HMAC request signing
        'max_request_size': 16 * 1024 * 1024,  # 16MB
        'allowed_origins': ['https://autoapply.co.nz'],
    }
    
    # Audit Logging Configuration
    AUDIT_CONFIG = {
        'log_all_requests': False,
        'log_failed_requests': True,
        'log_admin_actions': True,
        'log_data_access': True,
        'log_security_events': True,
        'retention_days': 365,  # Keep audit logs for 1 year
        'export_enabled': True,
    }
    
    # Malicious Pattern Detection
    MALICIOUS_PATTERNS = [
        # Path traversal
        '../', '..\\', '..\/', '..%2f', '..%5c',
        
        # Script injection
        '<script', '</script>', 'javascript:', 'vbscript:',
        'onload=', 'onerror=', 'onclick=', 'onmouseover=',
        
        # Code execution
        'eval(', 'exec(', 'system(', 'shell_exec(',
        'passthru(', 'file_get_contents(', 'include(',
        
        # SQL injection
        'union select', 'union all select', 'drop table',
        'delete from', 'insert into', 'update set',
        "' or '1'='1", '" or "1"="1', "' or 1=1--",
        
        # Command injection
        '|', '&&', '||', ';', '`', '$(',
        
        # File inclusion
        'file://', 'php://', 'data://', 'expect://',
        
        # XXE attacks
        '<!entity', '<!doctype', 'system "',
    ]
    
    # Bot Detection Patterns
    BOT_PATTERNS = [
        'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
        'python-requests', 'go-http-client', 'java/', 'php/',
        'postman', 'insomnia', 'httpie', 'rest-client',
    ]
    
    # Legitimate Bot Whitelist
    LEGITIMATE_BOTS = [
        'googlebot', 'bingbot', 'slurp', 'duckduckbot',
        'baiduspider', 'yandexbot', 'facebookexternalhit',
        'twitterbot', 'linkedinbot', 'whatsapp',
    ]
    
    # File Upload Security
    UPLOAD_CONFIG = {
        'max_file_size': 16 * 1024 * 1024,  # 16MB
        'allowed_extensions': ['.pdf', '.doc', '.docx', '.txt'],
        'scan_for_malware': True,
        'quarantine_suspicious': True,
        'virus_scan_timeout': 30,  # seconds
    }
    
    # Encryption Configuration
    ENCRYPTION_CONFIG = {
        'algorithm': 'AES-256-GCM',
        'key_rotation_days': 90,
        'backup_encryption': True,
        'database_encryption': True,
        'file_encryption': True,
    }
    
    # Compliance Features
    COMPLIANCE_CONFIG = {
        'gdpr_enabled': True,
        'ccpa_enabled': False,  # California Consumer Privacy Act
        'pipeda_enabled': False,  # Personal Information Protection and Electronic Documents Act (Canada)
        'privacy_by_design': True,
        'data_minimization': True,
        'purpose_limitation': True,
        'storage_limitation': True,
    }
    
    # Security Monitoring
    MONITORING_CONFIG = {
        'real_time_alerts': True,
        'anomaly_detection': True,
        'threat_intelligence': False,
        'security_dashboard': True,
        'automated_response': True,
        'incident_response': True,
    }
    
    # Backup and Recovery
    BACKUP_CONFIG = {
        'encrypted_backups': True,
        'backup_frequency': 'daily',
        'backup_retention_days': 30,
        'disaster_recovery': True,
        'backup_verification': True,
        'offsite_backup': True,
    }

class SecurityValidator:
    """Validates security configuration and settings"""
    
    @staticmethod
    def validate_password(password):
        """Validate password against security requirements"""
        config = SecurityConfig.PASSWORD_CONFIG
        
        if len(password) < config['min_length']:
            return False, f"Password must be at least {config['min_length']} characters"
        
        if config['require_uppercase'] and not any(c.isupper() for c in password):
            return False, "Password must contain at least one uppercase letter"
        
        if config['require_lowercase'] and not any(c.islower() for c in password):
            return False, "Password must contain at least one lowercase letter"
        
        if config['require_numbers'] and not any(c.isdigit() for c in password):
            return False, "Password must contain at least one number"
        
        if config['require_special_chars']:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                return False, "Password must contain at least one special character"
        
        return True, "Password meets security requirements"
    
    @staticmethod
    def validate_file_upload(filename, file_size):
        """Validate file upload against security requirements"""
        config = SecurityConfig.UPLOAD_CONFIG
        
        if file_size > config['max_file_size']:
            return False, f"File size exceeds maximum allowed ({config['max_file_size']} bytes)"
        
        file_ext = os.path.splitext(filename)[1].lower()
        if file_ext not in config['allowed_extensions']:
            return False, f"File type not allowed. Allowed types: {', '.join(config['allowed_extensions'])}"
        
        return True, "File upload is valid"
    
    @staticmethod
    def is_malicious_request(url, user_agent=None):
        """Check if request contains malicious patterns"""
        url_lower = url.lower()
        
        for pattern in SecurityConfig.MALICIOUS_PATTERNS:
            if pattern in url_lower:
                return True, f"Malicious pattern detected: {pattern}"
        
        if user_agent:
            ua_lower = user_agent.lower()
            
            # Check for bots
            for pattern in SecurityConfig.BOT_PATTERNS:
                if pattern in ua_lower:
                    # Check if it's a legitimate bot
                    if any(legit in ua_lower for legit in SecurityConfig.LEGITIMATE_BOTS):
                        return False, "Legitimate bot detected"
                    return True, f"Bot pattern detected: {pattern}"
        
        return False, "Request appears safe"

# Export configuration for easy access
security_config = SecurityConfig()
security_validator = SecurityValidator()
