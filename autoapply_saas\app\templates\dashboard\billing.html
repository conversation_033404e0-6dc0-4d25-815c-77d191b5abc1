{% extends "dashboard/base.html" %}

{% block dashboard_content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">Billing & Subscription</h1>
    </div>
</div>

<!-- Current Plan -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-crown me-2"></i>Current Plan
                </h5>
            </div>
            <div class="card-body">
                {% if current_user.has_active_subscription() %}
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-success bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="fas fa-crown text-success fa-lg"></i>
                                </div>
                                <div>
                                    <h4 class="fw-bold mb-1">Premium Subscription</h4>
                                    <p class="text-muted mb-0">Unlimited job applications with AI cover letters</p>
                                </div>
                            </div>
                            
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">$10</div>
                                        <small class="text-muted">per month</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">{{ current_user.subscription.days_remaining() }}</div>
                                        <small class="text-muted">days remaining</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">{{ current_user.job_applications.count() }}</div>
                                        <small class="text-muted">total applications</small>
                                    </div>
                                </div>
                            </div>
                            
                            {% if current_user.subscription.cancel_at_period_end %}
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Subscription Cancelling:</strong> Your subscription will end on 
                                    {{ current_user.subscription.current_period_end.strftime('%B %d, %Y') }}.
                                    You can reactivate it anytime before then.
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('payments.manage_subscription') }}" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>Manage Subscription
                                </a>
                                
                                {% if not current_user.subscription.cancel_at_period_end %}
                                    <button class="btn btn-outline-danger" onclick="cancelSubscription()">
                                        <i class="fas fa-times me-2"></i>Cancel Subscription
                                    </button>
                                {% else %}
                                    <button class="btn btn-outline-success" onclick="reactivateSubscription()">
                                        <i class="fas fa-undo me-2"></i>Reactivate Subscription
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                {% elif current_user.is_trial_active() %}
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-info bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="fas fa-gift text-info fa-lg"></i>
                                </div>
                                <div>
                                    <h4 class="fw-bold mb-1">Free Trial</h4>
                                    <p class="text-muted mb-0">7-day trial with up to 10 applications</p>
                                </div>
                            </div>
                            
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">{{ current_user.trial_applications_used }}</div>
                                        <small class="text-muted">applications used</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">{{ 10 - current_user.trial_applications_used }}</div>
                                        <small class="text-muted">remaining</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="h5 mb-0">{{ (current_user.trial_started_at + timedelta(days=7) - datetime.utcnow()).days }}</div>
                                        <small class="text-muted">days left</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="progress mt-3" style="height: 8px;">
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: {{ (current_user.trial_applications_used / 10 * 100) | round }}%"></div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-crown me-2"></i>Upgrade Now
                            </a>
                            <div class="mt-2">
                                <small class="text-muted">Unlimited applications for $10/month</small>
                            </div>
                        </div>
                    </div>
                    
                {% else %}
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-warning bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="fas fa-exclamation-triangle text-warning fa-lg"></i>
                                </div>
                                <div>
                                    <h4 class="fw-bold mb-1">No Active Plan</h4>
                                    <p class="text-muted mb-0">Subscribe to start automating your job applications</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket me-2"></i>Start Free Trial
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Usage Statistics -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Usage Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-2">
                                <i class="fas fa-paper-plane text-primary fa-lg"></i>
                            </div>
                            <div class="h4 mb-0">{{ current_user.get_applications_today() }}</div>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3 mb-2">
                                <i class="fas fa-calendar-week text-success fa-lg"></i>
                            </div>
                            <div class="h4 mb-0">{{ current_user.job_applications.filter(current_user.job_applications.c.applied_at >= (datetime.utcnow() - timedelta(days=7))).count() }}</div>
                            <small class="text-muted">This Week</small>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-2">
                                <i class="fas fa-calendar text-info fa-lg"></i>
                            </div>
                            <div class="h4 mb-0">{{ current_user.job_applications.filter(db.func.extract('month', current_user.job_applications.c.applied_at) == datetime.utcnow().month).count() }}</div>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-2">
                                <i class="fas fa-chart-bar text-warning fa-lg"></i>
                            </div>
                            <div class="h4 mb-0">{{ current_user.job_applications.count() }}</div>
                            <small class="text-muted">All Time</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Automation Sessions</h6>
                        <div class="d-flex justify-content-between">
                            <span>Completed:</span>
                            <span class="fw-bold">{{ current_user.automation_sessions.filter_by(status='completed').count() }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Failed:</span>
                            <span class="fw-bold">{{ current_user.automation_sessions.filter_by(status='failed').count() }}</span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold">Success Rate</h6>
                        {% set total_sessions = current_user.automation_sessions.count() %}
                        {% set completed_sessions = current_user.automation_sessions.filter_by(status='completed').count() %}
                        {% set success_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0 %}
                        
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ success_rate | round }}%"></div>
                        </div>
                        <div class="text-center">
                            <span class="fw-bold">{{ success_rate | round }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Billing Information -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>Billing Information
                </h5>
            </div>
            <div class="card-body">
                {% if current_user.subscription and current_user.subscription.stripe_customer_id %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Next Billing Date:</span>
                            <span class="fw-bold">
                                {{ current_user.subscription.current_period_end.strftime('%b %d, %Y') if current_user.subscription.current_period_end else 'N/A' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Amount:</span>
                            <span class="fw-bold">$10.00 NZD</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Status:</span>
                            {% if current_user.subscription.status == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% elif current_user.subscription.status == 'past_due' %}
                                <span class="badge bg-warning">Past Due</span>
                            {% elif current_user.subscription.status == 'canceled' %}
                                <span class="badge bg-danger">Canceled</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ current_user.subscription.status.title() }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid">
                        <a href="{{ url_for('payments.manage_subscription') }}" class="btn btn-outline-primary">
                            <i class="fas fa-cog me-2"></i>Manage Payment Method
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-credit-card text-muted fa-2x mb-3"></i>
                        <p class="text-muted">No billing information on file</p>
                        <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Payment Method
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pricing Plans -->
{% if not current_user.has_active_subscription() %}
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>Available Plans
                </h5>
            </div>
            <div class="card-body">
                <div class="row justify-content-center">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="pricing-card h-100 p-4 border rounded">
                            <div class="text-center">
                                <h4 class="fw-bold">Free Trial</h4>
                                <div class="price mb-3">
                                    <span class="display-4 fw-bold">$0</span>
                                    <span class="text-muted">/7 days</span>
                                </div>
                                <ul class="list-unstyled mb-4">
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Up to 10 applications</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>AI cover letters</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Basic tracking</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Email support</li>
                                </ul>
                                {% if not current_user.trial_started_at %}
                                    <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary w-100">
                                        Start Free Trial
                                    </a>
                                {% else %}
                                    <button class="btn btn-outline-secondary w-100" disabled>
                                        Trial Used
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="pricing-card h-100 p-4 border rounded border-primary position-relative">
                            <div class="badge bg-primary position-absolute top-0 start-50 translate-middle px-3 py-2">
                                Recommended
                            </div>
                            <div class="text-center">
                                <h4 class="fw-bold">Monthly Plan</h4>
                                <div class="price mb-3">
                                    <span class="display-4 fw-bold">$10</span>
                                    <span class="text-muted">/month</span>
                                </div>
                                <ul class="list-unstyled mb-4">
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Unlimited applications</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>AI cover letters</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Advanced tracking</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Priority support</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Cancel anytime</li>
                                </ul>
                                <a href="{{ url_for('payments.subscribe') }}" class="btn btn-primary w-100">
                                    Subscribe Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
async function cancelSubscription() {
    if (!confirm('Are you sure you want to cancel your subscription? You will still have access until the end of your current billing period.')) {
        return;
    }
    
    try {
        const response = await fetch('/payments/cancel-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ at_period_end: true })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Subscription cancelled successfully', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(result.error || 'Failed to cancel subscription', 'error');
        }
    } catch (error) {
        console.error('Error cancelling subscription:', error);
        showToast('Network error', 'error');
    }
}

function reactivateSubscription() {
    // Redirect to Stripe customer portal for reactivation
    window.location.href = '{{ url_for("payments.manage_subscription") }}';
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast position-fixed bottom-0 end-0 m-3 bg-${type === 'error' ? 'danger' : type} text-white`;
    toast.innerHTML = `
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>
{% endblock %}
