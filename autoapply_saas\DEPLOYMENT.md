# AutoApply.co.nz Deployment Guide

This guide covers the complete deployment process for AutoApply.co.nz, from development to production.

## 🏗️ Infrastructure Overview

### Architecture
- **Application**: Flask web application with Gunicorn WSGI server
- **Database**: PostgreSQL 15 with automated backups
- **Cache**: Redis for session storage and job queues
- **Queue**: Celery for background job processing
- **Proxy**: Nginx reverse proxy with SSL termination
- **Monitoring**: Prometheus, Grafana, and Loki stack
- **Container**: Docker with Docker Compose orchestration

### Production Environment
- **Cloud Provider**: AWS (recommended) or any Docker-compatible hosting
- **Load Balancer**: Application Load Balancer (ALB) with SSL
- **Database**: RDS PostgreSQL with Multi-AZ deployment
- **Cache**: ElastiCache Redis with clustering
- **Storage**: S3 for backups and static files
- **Monitoring**: CloudWatch integration with custom dashboards

## 🚀 Quick Start Deployment

### Prerequisites
```bash
# Install required tools
sudo apt update
sudo apt install -y docker.io docker-compose git curl

# Clone repository
git clone https://github.com/your-org/autoapply-saas.git
cd autoapply-saas
```

### Environment Configuration
```bash
# Copy environment template
cp .env.example .env.prod

# Edit production environment variables
nano .env.prod
```

Required environment variables:
```env
# Database
DB_PASSWORD=your_secure_database_password

# Application
SECRET_KEY=your_secret_key_here
FLASK_ENV=production

# Stripe Integration
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# OpenAI Integration
OPENAI_API_KEY=sk-...

# Monitoring
GRAFANA_PASSWORD=secure_grafana_password

# AWS Backup (optional)
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
S3_BACKUP_BUCKET=autoapply-backups
```

### Production Deployment
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh deploy
```

## 🔧 Manual Deployment Steps

### 1. Infrastructure Setup
```bash
# Create deployment directory
sudo mkdir -p /opt/autoapply
sudo chown $USER:$USER /opt/autoapply
cd /opt/autoapply

# Clone repository
git clone https://github.com/your-org/autoapply-saas.git .
```

### 2. SSL Certificate Setup
```bash
# Create SSL directory
mkdir -p nginx/ssl

# Generate self-signed certificate (for testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/autoapply.co.nz.key \
  -out nginx/ssl/autoapply.co.nz.crt

# For production, use Let's Encrypt or purchased certificate
```

### 3. Database Initialization
```bash
# Start database container
docker-compose -f docker-compose.prod.yml up -d db redis

# Wait for database to be ready
sleep 30

# Run database migrations
docker-compose -f docker-compose.prod.yml run --rm web flask db upgrade

# Create admin user (optional)
docker-compose -f docker-compose.prod.yml run --rm web flask create-admin
```

### 4. Application Deployment
```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f web
```

### 5. Health Check
```bash
# Test application health
curl -f http://localhost/health

# Test HTTPS (if SSL configured)
curl -f https://autoapply.co.nz/health
```

## 🌐 AWS Production Deployment

### Using Terraform
```bash
# Navigate to Terraform directory
cd terraform

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="production.tfvars"

# Apply infrastructure
terraform apply -var-file="production.tfvars"
```

### ECS Deployment
```bash
# Build and push Docker image
docker build -f Dockerfile.prod -t autoapply:latest .
docker tag autoapply:latest your-account.dkr.ecr.region.amazonaws.com/autoapply:latest
docker push your-account.dkr.ecr.region.amazonaws.com/autoapply:latest

# Update ECS service
aws ecs update-service --cluster autoapply-cluster --service autoapply-service --force-new-deployment
```

## 🔄 CI/CD Pipeline

### GitHub Actions Setup
1. Configure repository secrets:
   ```
   SSH_PRIVATE_KEY: Your deployment server SSH key
   SERVER_HOST: Your server IP/hostname
   SERVER_USER: Deployment user
   DB_PASSWORD: Database password
   SECRET_KEY: Application secret key
   STRIPE_*: Stripe API keys
   MAIL_*: Email configuration
   OPENAI_API_KEY: OpenAI API key
   ```

2. Push to main branch triggers automatic deployment

### Manual CI/CD Commands
```bash
# Run tests locally
pytest --cov=app

# Build production image
docker build -f Dockerfile.prod -t autoapply:latest .

# Deploy to staging
./scripts/deploy.sh deploy

# Deploy to production (with approval)
./scripts/deploy.sh deploy --environment=production
```

## 📊 Monitoring Setup

### Accessing Monitoring Dashboards
- **Grafana**: http://your-server:3000 (admin/your_grafana_password)
- **Prometheus**: http://your-server:9090
- **Application Logs**: `docker-compose logs -f web`

### Key Metrics to Monitor
- Application response time and error rate
- Database connection pool and query performance
- Redis memory usage and hit rate
- Celery queue length and worker status
- System resources (CPU, memory, disk)
- SSL certificate expiration

### Alerting Configuration
```bash
# Configure Slack notifications
export SLACK_WEBHOOK_URL="https://hooks.slack.com/services/..."

# Test alert
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"Test deployment notification"}' \
  $SLACK_WEBHOOK_URL
```

## 🔒 Security Considerations

### SSL/TLS Configuration
- Use strong SSL ciphers and protocols
- Enable HSTS headers
- Regular certificate renewal

### Database Security
- Use strong passwords
- Enable encryption at rest
- Regular security updates
- Network isolation

### Application Security
- Regular dependency updates
- Security header configuration
- Rate limiting and DDoS protection
- Regular security audits

## 🔧 Maintenance Tasks

### Regular Maintenance
```bash
# Update Docker images
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Database backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U autoapply autoapply_prod > backup.sql

# Clean up old Docker images
docker image prune -f

# View system resources
docker stats

# Check logs for errors
docker-compose -f docker-compose.prod.yml logs --tail=100 web | grep ERROR
```

### Scaling Operations
```bash
# Scale Celery workers
docker-compose -f docker-compose.prod.yml up -d --scale celery_worker=4

# Monitor queue length
docker-compose -f docker-compose.prod.yml exec redis redis-cli llen automation_queue

# Database performance tuning
docker-compose -f docker-compose.prod.yml exec db psql -U autoapply -c "SELECT * FROM pg_stat_activity;"
```

## 🆘 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check container logs
docker-compose -f docker-compose.prod.yml logs web

# Check environment variables
docker-compose -f docker-compose.prod.yml exec web env | grep FLASK

# Test database connection
docker-compose -f docker-compose.prod.yml exec web python -c "from app import db; print(db.engine.execute('SELECT 1').scalar())"
```

#### Database Connection Issues
```bash
# Check database status
docker-compose -f docker-compose.prod.yml exec db pg_isready -U autoapply

# Check network connectivity
docker-compose -f docker-compose.prod.yml exec web ping db

# Reset database connection
docker-compose -f docker-compose.prod.yml restart web
```

#### High Memory Usage
```bash
# Check container resource usage
docker stats

# Restart services to clear memory
docker-compose -f docker-compose.prod.yml restart

# Check for memory leaks
docker-compose -f docker-compose.prod.yml exec web python -c "import psutil; print(psutil.virtual_memory())"
```

### Emergency Procedures

#### Rollback Deployment
```bash
# Automatic rollback
./scripts/deploy.sh rollback

# Manual rollback
git checkout previous-working-commit
docker-compose -f docker-compose.prod.yml up -d --force-recreate
```

#### Database Recovery
```bash
# Restore from backup
docker-compose -f docker-compose.prod.yml exec -T db psql -U autoapply -d autoapply_prod < backup.sql

# Point-in-time recovery (AWS RDS)
aws rds restore-db-instance-to-point-in-time --source-db-instance-identifier autoapply-db --target-db-instance-identifier autoapply-db-restored --restore-time 2024-01-01T12:00:00Z
```

## 📞 Support

### Getting Help
- **Documentation**: Check this deployment guide and application README
- **Logs**: Always check application and system logs first
- **Monitoring**: Use Grafana dashboards to identify issues
- **Community**: GitHub issues for bug reports and feature requests

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **Development Team**: <EMAIL>
- **Infrastructure**: <EMAIL>

---

**Note**: This deployment guide assumes a production environment. For development setup, use `docker-compose.yml` instead of `docker-compose.prod.yml` and adjust environment variables accordingly.
