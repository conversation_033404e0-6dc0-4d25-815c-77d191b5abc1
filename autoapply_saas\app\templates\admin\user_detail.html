{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
                <h1 class="h3 mb-0">User Details: {{ user.first_name }} {{ user.last_name }}</h1>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="sendUserEmail()">
                    <i class="fas fa-envelope me-2"></i>Send Email
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-2"></i>Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})">
                                {% if user.is_active %}
                                    <i class="fas fa-ban me-2"></i>Deactivate User
                                {% else %}
                                    <i class="fas fa-check me-2"></i>Activate User
                                {% endif %}
                            </a>
                        </li>
                        {% if user.trial_started_at %}
                        <li>
                            <a class="dropdown-item" href="#" onclick="resetUserTrial({{ user.id }})">
                                <i class="fas fa-redo me-2"></i>Reset Trial
                            </a>
                        </li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="#" onclick="deleteUser({{ user.id }})">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Overview -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="avatar bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px; font-size: 1.5rem;">
                    {{ user.first_name[0].upper() }}{{ user.last_name[0].upper() }}
                </div>
                <h5 class="fw-bold">{{ user.first_name }} {{ user.last_name }}</h5>
                <p class="text-muted mb-2">{{ user.email }}</p>
                <div class="d-flex justify-content-center gap-2">
                    {% if user.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">Inactive</span>
                    {% endif %}
                    
                    {% if user.is_verified %}
                        <span class="badge bg-info">Verified</span>
                    {% else %}
                        <span class="badge bg-warning">Unverified</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-paper-plane text-primary fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ user_stats.total_applications }}</h4>
                <p class="text-muted mb-0">Total Applications</p>
                <small class="text-success">{{ user_stats.applications_today }} today</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-robot text-success fa-2x mb-3"></i>
                <h4 class="fw-bold">{{ user_stats.total_sessions }}</h4>
                <p class="text-muted mb-0">Automation Sessions</p>
                <small class="text-info">{{ user_stats.completed_sessions }} completed</small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                {% if user.has_active_subscription() %}
                    <i class="fas fa-crown text-warning fa-2x mb-3"></i>
                    <h4 class="fw-bold">Premium</h4>
                    <p class="text-muted mb-0">Subscription</p>
                    <small class="text-success">Active</small>
                {% elif user.is_trial_active() %}
                    <i class="fas fa-gift text-info fa-2x mb-3"></i>
                    <h4 class="fw-bold">Trial</h4>
                    <p class="text-muted mb-0">{{ user_stats.trial_applications_used }}/10 used</p>
                    <small class="text-info">Active</small>
                {% else %}
                    <i class="fas fa-user text-muted fa-2x mb-3"></i>
                    <h4 class="fw-bold">Free</h4>
                    <p class="text-muted mb-0">No active plan</p>
                    <small class="text-muted">Inactive</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Information -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-4 fw-bold">Email:</div>
                    <div class="col-sm-8">{{ user.email }}</div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-4 fw-bold">Full Name:</div>
                    <div class="col-sm-8">{{ user.first_name }} {{ user.last_name }}</div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-4 fw-bold">Member Since:</div>
                    <div class="col-sm-8">{{ user.created_at.strftime('%B %d, %Y at %H:%M') }}</div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-4 fw-bold">Last Login:</div>
                    <div class="col-sm-8">
                        {% if user_stats.last_login %}
                            {{ user_stats.last_login.strftime('%B %d, %Y at %H:%M') }}
                        {% else %}
                            <span class="text-muted">Never</span>
                        {% endif %}
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-4 fw-bold">Account Age:</div>
                    <div class="col-sm-8">{{ user_stats.account_age_days }} days</div>
                </div>
                {% if user.email_verified_at %}
                <hr>
                <div class="row">
                    <div class="col-sm-4 fw-bold">Email Verified:</div>
                    <div class="col-sm-8">{{ user.email_verified_at.strftime('%B %d, %Y at %H:%M') }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Usage Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-primary">{{ user_stats.total_applications }}</div>
                            <small class="text-muted">Total Applications</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-success">{{ user_stats.applications_today }}</div>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-info">{{ user_stats.total_sessions }}</div>
                            <small class="text-muted">Sessions</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-0 text-warning">{{ user_stats.completed_sessions }}</div>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                </div>
                
                {% if user_stats.total_sessions > 0 %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6 class="fw-bold">Session Success Rate</h6>
                        {% set success_rate = (user_stats.completed_sessions / user_stats.total_sessions * 100) %}
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ success_rate }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted">
                            <span>{{ user_stats.completed_sessions }} / {{ user_stats.total_sessions }} sessions</span>
                            <span>{{ success_rate|round }}% success rate</span>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if user.is_trial_active() %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6 class="fw-bold">Trial Usage</h6>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar bg-info" role="progressbar" 
                                 style="width: {{ (user_stats.trial_applications_used / 10 * 100) }}%"></div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted">
                            <span>{{ user_stats.trial_applications_used }} / 10 applications used</span>
                            <span>{{ 10 - user_stats.trial_applications_used }} remaining</span>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-paper-plane me-2"></i>Recent Applications
                </h5>
            </div>
            <div class="card-body">
                {% if recent_applications %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Job Title</th>
                                    <th>Company</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for app in recent_applications %}
                                <tr>
                                    <td>
                                        <div class="fw-bold small">{{ app.job_title }}</div>
                                    </td>
                                    <td>{{ app.company_name }}</td>
                                    <td>
                                        {% if app.application_status == 'submitted' %}
                                            <span class="badge bg-success">Submitted</span>
                                        {% elif app.application_status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ app.application_status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ app.applied_at.strftime('%b %d') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-inbox text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No applications yet</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>Recent Sessions
                </h5>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>Session</th>
                                    <th>Status</th>
                                    <th>Results</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in recent_sessions %}
                                <tr>
                                    <td>
                                        <div class="fw-bold small">#{{ session.id }}</div>
                                    </td>
                                    <td>
                                        {% if session.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% elif session.status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% elif session.status == 'running' %}
                                            <span class="badge bg-primary">Running</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ session.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ session.applications_submitted or 0 }} apps</small>
                                    </td>
                                    <td>
                                        <small>{{ session.started_at.strftime('%b %d') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-robot text-muted fa-2x mb-2"></i>
                        <div class="text-muted">No sessions yet</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
async function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    
    if (!confirm(`Are you sure you want to ${action} this user?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to update user status', 'error');
        }
    } catch (error) {
        console.error('Error toggling user status:', error);
        showAdminToast('Network error', 'error');
    }
}

async function resetUserTrial(userId) {
    if (!confirm('Are you sure you want to reset this user\'s trial? This will give them a fresh 7-day trial with 10 applications.')) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/users/${userId}/reset-trial`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAdminToast(result.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAdminToast(result.error || 'Failed to reset trial', 'error');
        }
    } catch (error) {
        console.error('Error resetting trial:', error);
        showAdminToast('Network error', 'error');
    }
}

function deleteUser(userId) {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone and will remove all their data.')) {
        return;
    }
    
    // Implementation for user deletion
    showAdminToast('User deletion feature coming soon!', 'info');
}

function sendUserEmail() {
    // Implementation for sending email to user
    showAdminToast('Email feature coming soon!', 'info');
}
</script>
{% endblock %}
