"""
Email utilities for authentication
"""

from flask import current_app, render_template, url_for
from flask_mail import Message
from app import mail
import jwt
from datetime import datetime, timed<PERSON>ta

def send_email(subject, sender, recipients, text_body, html_body):
    """Send email using Flask-Mail"""
    msg = Message(subject, sender=sender, recipients=recipients)
    msg.body = text_body
    msg.html = html_body
    mail.send(msg)

def send_verification_email(user):
    """Send email verification email"""
    token = user.generate_verification_token()
    verification_url = url_for('auth.verify_email', token=token, _external=True)
    
    subject = 'Verify Your AutoApply.co.nz Account'
    sender = current_app.config['MAIL_DEFAULT_SENDER']
    recipients = [user.email]
    
    text_body = f'''
Hello {user.first_name},

Welcome to AutoApply.co.nz! Please verify your email address by clicking the link below:

{verification_url}

This link will expire in 1 hour.

If you did not create an account, please ignore this email.

Best regards,
The AutoApply.co.nz Team
'''
    
    html_body = f'''
<html>
<body>
    <h2>Welcome to AutoApply.co.nz!</h2>
    <p>Hello {user.first_name},</p>
    
    <p>Thank you for creating an account with AutoApply.co.nz. To complete your registration, please verify your email address by clicking the button below:</p>
    
    <p style="text-align: center; margin: 30px 0;">
        <a href="{verification_url}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Verify Email Address
        </a>
    </p>
    
    <p>Or copy and paste this link into your browser:</p>
    <p><a href="{verification_url}">{verification_url}</a></p>
    
    <p><strong>This link will expire in 1 hour.</strong></p>
    
    <p>If you did not create an account, please ignore this email.</p>
    
    <hr>
    <p style="color: #666; font-size: 12px;">
        Best regards,<br>
        The AutoApply.co.nz Team
    </p>
</body>
</html>
'''
    
    send_email(subject, sender, recipients, text_body, html_body)

def send_password_reset_email(user):
    """Send password reset email"""
    token = jwt.encode(
        {'reset_password': user.id, 'exp': datetime.utcnow() + timedelta(hours=1)},
        current_app.config['SECRET_KEY'],
        algorithm='HS256'
    )
    
    reset_url = url_for('auth.reset_password', token=token, _external=True)
    
    subject = 'Reset Your AutoApply.co.nz Password'
    sender = current_app.config['MAIL_DEFAULT_SENDER']
    recipients = [user.email]
    
    text_body = f'''
Hello {user.first_name},

You have requested to reset your password for your AutoApply.co.nz account.

Please click the link below to reset your password:

{reset_url}

This link will expire in 1 hour.

If you did not request a password reset, please ignore this email.

Best regards,
The AutoApply.co.nz Team
'''
    
    html_body = f'''
<html>
<body>
    <h2>Password Reset Request</h2>
    <p>Hello {user.first_name},</p>
    
    <p>You have requested to reset your password for your AutoApply.co.nz account.</p>
    
    <p style="text-align: center; margin: 30px 0;">
        <a href="{reset_url}" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset Password
        </a>
    </p>
    
    <p>Or copy and paste this link into your browser:</p>
    <p><a href="{reset_url}">{reset_url}</a></p>
    
    <p><strong>This link will expire in 1 hour.</strong></p>
    
    <p>If you did not request a password reset, please ignore this email. Your password will remain unchanged.</p>
    
    <hr>
    <p style="color: #666; font-size: 12px;">
        Best regards,<br>
        The AutoApply.co.nz Team
    </p>
</body>
</html>
'''
    
    send_email(subject, sender, recipients, text_body, html_body)

def send_welcome_email(user):
    """Send welcome email after successful verification"""
    subject = 'Welcome to AutoApply.co.nz - Your Job Application Automation Starts Now!'
    sender = current_app.config['MAIL_DEFAULT_SENDER']
    recipients = [user.email]
    
    dashboard_url = url_for('dashboard.index', _external=True)
    
    text_body = f'''
Hello {user.first_name},

Welcome to AutoApply.co.nz! Your account has been successfully verified and you're ready to start automating your job applications.

Here's what you can do now:

1. Upload your resume/CV
2. Set your job preferences
3. Start your first automation session
4. Track your application progress

Your 7-day free trial includes:
- Up to 10 job applications
- AI-generated cover letters
- Automatic form filling
- Application tracking

Get started: {dashboard_url}

Need help? Check out our getting started guide or contact our support team.

Best regards,
The AutoApply.co.nz Team
'''
    
    html_body = f'''
<html>
<body>
    <h2>Welcome to AutoApply.co.nz!</h2>
    <p>Hello {user.first_name},</p>
    
    <p>Your account has been successfully verified and you're ready to start automating your job applications on Seek.co.nz!</p>
    
    <h3>Here's what you can do now:</h3>
    <ul>
        <li>📄 Upload your resume/CV</li>
        <li>⚙️ Set your job preferences</li>
        <li>🚀 Start your first automation session</li>
        <li>📊 Track your application progress</li>
    </ul>
    
    <h3>Your 7-day free trial includes:</h3>
    <ul>
        <li>✅ Up to 10 job applications</li>
        <li>🤖 AI-generated cover letters</li>
        <li>📝 Automatic form filling</li>
        <li>📈 Application tracking</li>
    </ul>
    
    <p style="text-align: center; margin: 30px 0;">
        <a href="{dashboard_url}" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px;">
            Get Started Now
        </a>
    </p>
    
    <p>Need help? Check out our getting started guide or contact our support team.</p>
    
    <hr>
    <p style="color: #666; font-size: 12px;">
        Best regards,<br>
        The AutoApply.co.nz Team
    </p>
</body>
</html>
'''
    
    send_email(subject, sender, recipients, text_body, html_body)
