# AutoApply.co.nz Environment Configuration
# Copy this file to .env and update with your actual values

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=postgresql://autoapply:autoapply_password@localhost:5432/autoapply_dev

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Email Configuration (SendGrid)
MAIL_USERNAME=your-sendgrid-username
MAIL_PASSWORD=your-sendgrid-api-key
MAIL_DEFAULT_SENDER=<EMAIL>

# Stripe Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_MONTHLY_PRICE_ID=price_your_monthly_price_id

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key

# Chrome/Selenium Configuration
CHROME_DRIVER_PATH=/usr/local/bin/chromedriver
CHROME_BINARY_PATH=/usr/bin/google-chrome

# Security Configuration
SESSION_COOKIE_SECURE=false
WTF_CSRF_ENABLED=true

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=INFO

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=/app/uploads

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/0

# Application Settings
JOBS_PER_PAGE=20
MAX_APPLICATIONS_PER_DAY=50
FREE_TRIAL_DAYS=7
FREE_TRIAL_APPLICATIONS=10
AUTOMATION_TIMEOUT=300
MAX_CONCURRENT_AUTOMATIONS=5
